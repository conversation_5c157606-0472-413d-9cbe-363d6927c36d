FROM registry.cn-beijing.aliyuncs.com/ijx-public/opentelemetry-javaagent:1.26.0 as opentelemetry
FROM harbor.ijx.icu/ijx-public/eclipse-temurin:17-jre-alpine
RUN apk add tzdata && cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone \
    && apk del tzdata
VOLUME /tmp
ARG JAR_FILE=target/edu-analytics-manager.jar
COPY ${JAR_FILE} edu-analytics-manager.jar
COPY --from=opentelemetry / /
ENTRYPOINT ["java","-Djava.security.egd=file:/dev/./urandom","-jar","/edu-analytics-manager.jar"]