<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.edu.analytics.mapper.ExamMapper">

    <!-- 获取班级平均错题数 -->
    <select id="getExamInfo" resultType="com.joinus.edu.analytics.model.dto.ExamInfoDto">
        SELECT
            distinct on (mq.id)
            me.id                                        "examId",
               mq.id                                        "questionId",
               me.name                                      "examName",
               mq.question_type,
               mq.content                                   "questionContent",
               meq.sort_no,
               mq.difficulty,
               mqa.answer,
               mqa.content                                  "analysis",
               STRING_AGG(f.oss_url, ',')                AS oss_url,
               STRING_AGG(qkp.knowledge_point_name, ',') AS knowledge_point_names
        FROM math_exams me
                 INNER JOIN math_exam_questions meq ON me.id = meq.exam_id
                 INNER JOIN math_questions mq ON mq.id = meq.question_id
                 LEFT JOIN question_knowledge_point qkp ON qkp.question_id = mq.id
                 left join math_question_answers qar on qar.question_id = mq.id
                 left join math_answers mqa on qar.answer_id = mqa.id
                 left join math_question_files qf on qf.question_id = mq.id
                 left join files f on f.id = qf.file_id
        WHERE me.id = #{examId}
        GROUP BY me.id,
                 mq.id,
                 me.name,
                 mq.question_type,
                 mq.content,
                 meq.sort_no,
                 mq.difficulty,
                 mqa.answer,
                 mqa.content,
                 meq.question_id
        order by mq.id,sort_no
    </select>
</mapper>
