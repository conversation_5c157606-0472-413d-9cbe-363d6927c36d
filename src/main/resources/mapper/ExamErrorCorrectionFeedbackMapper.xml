<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.edu.analytics.mapper.ExamErrorCorrectionFeedbackMapper">

    <select id="getExamErrorCorrectionFeedbackList"
            resultType="com.joinus.edu.analytics.model.result.ExamErrorCorrectionFeedbackPo">
        WITH RankedFeedback AS (
        SELECT
        ef.id,
        ef.created_at,
        ef.updated_at,
        ef.deleted_at,
        ef.student_id,
        ef.parent_id,
        ef.parent_name,
        ef.telephone_number,
        ef.exam_name,
        ef.exam_id,
        ef.result,
        ef.handler_id,
        ef.handler_name,
        ef.finished_at,
        ef.feedback_type,
        (SELECT COUNT(DISTINCT mef.file_id)
        FROM personal_exam pe
        LEFT JOIN math_exam_files mef ON pe.exam_id = mef.exam_id
        WHERE pe.exam_id = ef.exam_id and type = 'HANDWRITING_REMOVED_PAPER' ) AS examNum,
        ROW_NUMBER() OVER (PARTITION BY ef.exam_id ORDER BY ef.created_at DESC) AS rn
        FROM exam_error_correction_feedback ef
        WHERE ef.deleted_at IS NULL
        <if test="vo.parentName != null and vo.parentName != ''">
            and ef.parent_name like '%' || #{vo.parentName} || '%'
        </if>
        <if test="vo.tel != null and vo.tel != ''">
            and ef.telephone_number = #{vo.tel}
        </if>
        <if test="vo.examName != null and vo.examName != ''">
            and ef.exam_name like '%' || #{vo.examName} || '%'
        </if>
        <if test="vo.handlerName != null and vo.handlerName != ''">
            and ef.handler_name like '%' || #{vo.handlerName} || '%'
        </if>
        <if test="vo.result != null and vo.result != ''">
            and ef.result = #{vo.result}
        </if>
        <if test="null != vo.createTimeStart">
            and  <![CDATA[ ef.created_at >= to_date(#{vo.createTimeStart},'yyyy-mm-dd')]]>
        </if>
        <if test="null != vo.createTimeEnd">
            and  <![CDATA[ ef.created_at <= to_date(#{vo.createTimeEnd},'yyyy-mm-dd')+1]]>
        </if>
        <if test="vo.feedbackType != null and vo.feedbackType != ''">
            and ef.feedback_type = #{vo.feedbackType}
        </if>
        <if test="vo.examId != null and vo.examId != ''">
            and ef.exam_id = #{vo.examId} :: uuid
        </if>
        )
        SELECT
        id,
        created_at,
        updated_at,
        deleted_at,
        student_id,
        parent_id,
        parent_name,
        telephone_number,
        exam_name,
        exam_id,
        result,
        handler_id,
        handler_name,
        finished_at,
        feedback_type,
        examNum
        FROM RankedFeedback
        WHERE rn = 1
        ORDER BY created_at DESC
    </select>
    <select id="selectStudentInfoByStudentId"
            resultType="com.joinus.edu.analytics.model.result.ExamErrorCorrectionFeedbackPo">
        select grade_name,school_name from view_active_students where student_id = #{studentId}
    </select>
</mapper>
