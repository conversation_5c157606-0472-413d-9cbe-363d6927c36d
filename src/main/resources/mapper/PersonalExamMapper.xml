<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.edu.analytics.mapper.PersonalExamMapper">

    <select id="getStudentExamPage" resultType="com.joinus.edu.analytics.model.dto.PersonalExamDto">
        select pe.id as personalExamId,
               pe.exam_id         as examId,
               pe.exam_name       as examName,
               pe.updated_at     ,
               pe.grade           as grade,
               pe.publisher       as publisher,
               r.result     as analyzeResult,
               r.id as examAnalyzeResultId
        from personal_exam pe
        LEFT JOIN exam_analyze_result r on r.personal_exam_id = pe.id and r.deleted_at is null
        where pe.deleted_at is null
        and pe.student_id = #{param.studentId}
        <if test="param.examName != null">
            and pe.exam_name like concat('%',#{param.examName},'%')
        </if>
        order by pe.updated_at desc
    </select>
</mapper>
