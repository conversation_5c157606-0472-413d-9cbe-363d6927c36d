<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.edu.analytics.mapper.ExamAnalyzeResultMapper">

    <!-- 根据班级ID列表统计考试人数大于10的考试数量 -->
    <select id="countExamsWithMoreThan10StudentsByClass" resultType="int">
        WITH exams_with_enough_students AS (
            SELECT
                e.exam_id,
                COUNT(DISTINCT e.student_id) as student_count
            FROM
                exam_analyze_result e
                    JOIN
                view_active_students v ON e.student_id = v.student_id
            WHERE
                e.result = 'FINISHED'
              AND v.class_id IN
                <foreach collection="classIds" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            GROUP BY
                e.exam_id
            HAVING
                COUNT(DISTINCT e.student_id) > 0
        )
        SELECT
            COUNT(exam_id) as exam_count
        FROM
            exams_with_enough_students
    </select>
    
    <!-- 获取最近一个月内参与人数超过10的试卷ID列表 -->
    <select id="getRecentExamsWithMoreThan10Students" resultType="java.util.UUID">
        SELECT
            ear.exam_id
        FROM
            exam_analyze_result ear
        WHERE
            ear.result = 'FINISHED'
            AND ear.deleted_at IS NULL
            AND ear.created_at >= NOW() - INTERVAL '30 days'
        GROUP BY
            ear.exam_id
        HAVING
            COUNT(DISTINCT ear.student_id) >= 10
        ORDER BY
            MAX(ear.created_at) DESC
    </select>
    
    <!-- 获取最近一个月内参与人数超过10的试卷ID列表（分页版本） -->
    <select id="getRecentExamsWithMoreThan10StudentsPaged" resultType="java.lang.String">
        SELECT
            ear.exam_id::text
        FROM
            exam_analyze_result ear
        WHERE
            ear.result = 'FINISHED'
            AND ear.deleted_at IS NULL
            AND ear.created_at >= NOW() - INTERVAL '30 days'
        GROUP BY
            ear.exam_id
        HAVING
            COUNT(DISTINCT ear.student_id) >= 10
        ORDER BY
            MAX(ear.created_at) DESC
        LIMIT #{pageSize}
        OFFSET (#{pageNum} - 1) * #{pageSize}
    </select>
    
    <!-- 获取最近一个月内参与人数超过10的试卷总数 -->
    <select id="countRecentExamsWithMoreThan10Students" resultType="java.lang.Integer">
        SELECT
            COUNT(*)
        FROM
            (
                SELECT
                    ear.exam_id
                FROM
                    exam_analyze_result ear
                WHERE
                    ear.result = 'FINISHED'
                    AND ear.deleted_at IS NULL
                    AND ear.created_at >= NOW() - INTERVAL '30 days'
                GROUP BY
                    ear.exam_id
                HAVING
                    COUNT(DISTINCT ear.student_id) >= 10
            ) AS exams_count
    </select>
    
    <!-- 获取试卷基本信息 -->
    <select id="getExamBasicInfo" resultType="com.joinus.edu.analytics.model.dto.RecentExamDTO">
        SELECT
            me.id "examId",
            me.name "examName"
        FROM
            math_exams me
        WHERE
            me.id = #{examId}::uuid
    </select>
    
    <!-- 获取试卷的班级总人数和参与人数 -->
    <select id="getExamParticipationInfo" resultType="java.util.HashMap">
        SELECT
            COUNT(DISTINCT vas.student_id) AS total_students,
            COUNT(DISTINCT ear.student_id) AS participated_students
        FROM
            exam_analyze_result ear
        JOIN
            personal_exam pe ON ear.personal_exam_id = pe.id
        JOIN
            view_active_students vas ON ear.student_id = vas.student_id
        WHERE
            ear.exam_id = #{examId}::uuid
            AND ear.result = 'FINISHED'
            AND ear.deleted_at IS NULL
            AND pe.deleted_at IS NULL
    </select>
    
    <!-- 获取试卷的正确率 -->
    <select id="getExamCorrectRate" resultType="java.lang.Double">
        SELECT
            AVG(CAST(jsonb_extract_path_text(ear.correct_rate, 'value') AS DECIMAL)) AS correct_rate
        FROM
            exam_analyze_result ear
        WHERE
            ear.exam_id = #{examId}::uuid
            AND ear.result = 'FINISHED'
            AND ear.deleted_at IS NULL
    </select>
    
    <!-- 获取试卷的平均错题量 -->
    <select id="getExamAvgWrongQuestions" resultType="java.lang.Integer">
        SELECT
            COUNT(*) / COUNT(DISTINCT pe.student_id) AS avg_wrong_questions
        FROM
            personal_exam_question peq
        JOIN
            personal_exam pe ON peq.personal_exam_id = pe.id
        WHERE
            pe.exam_id = #{examId}::uuid
            AND peq.result = 'mistake'
            AND pe.deleted_at IS NULL
            AND peq.deleted_at IS NULL
    </select>
    
    <!-- 获取试卷的薄弱知识点数量 -->
    <select id="getExamWeakKnowledgePoints" resultType="java.lang.Integer">
        SELECT
            COUNT(DISTINCT qkp.knowledge_point_id) AS weak_knowledge_points
        FROM
            question_knowledge_point qkp
        JOIN
            personal_exam_question peq ON qkp.question_id = peq.question_id
        JOIN
            personal_exam pe ON peq.personal_exam_id = pe.id
        WHERE
            pe.exam_id = #{examId}::uuid
            AND peq.result = 'mistake'
            AND pe.deleted_at IS NULL
            AND peq.deleted_at IS NULL
            AND qkp.deleted_at IS NULL
    </select>
    
    <!-- 获取试卷的班级列表 -->
    <select id="getExamClassList" resultType="java.util.HashMap">
        SELECT DISTINCT
            vas.class_id,
            vas.class_name
        FROM
            exam_analyze_result ear
        JOIN
            view_active_students vas ON ear.student_id = vas.student_id
        WHERE
            ear.exam_id = #{examId}::uuid
            AND ear.result = 'FINISHED'
            AND ear.deleted_at IS NULL
        ORDER BY
            vas.class_id
    </select>
    
    <!-- 获取班级下学生总人数 -->
    <select id="getClassTotalStudents" resultType="java.lang.Integer">
        SELECT 
            COUNT(1) 
        FROM 
            view_active_students vas
        WHERE 
            vas.class_id = #{classId}
    </select>
    
    <!-- 获取班级所有学生（分页） -->
    <select id="getClassStudents" resultType="java.util.Map">
        SELECT 
            s.student_id as "studentId",
            s.student_name as "studentName",
            s.the_number as "studentNo",
            s.class_name as "className",
            s.grade_name as "gradeName"
        FROM 
            view_active_students s
        WHERE 
            s.class_id = #{classId} 
        ORDER BY 
            s.the_number ASC
    </select>
    
    <!-- 获取试卷的班级参与人数和总人数 -->
    <select id="getExamParticipationInfoByClass" resultType="java.util.HashMap">
        SELECT
            (SELECT COUNT(1) FROM view_active_students vas WHERE vas.class_id = #{classId}) AS total_students,
            COUNT(DISTINCT ear.student_id) AS participated_students
        FROM
            exam_analyze_result ear
        JOIN
            personal_exam pe ON ear.personal_exam_id = pe.id
        JOIN
            view_active_students vas ON ear.student_id = vas.student_id
        WHERE
            ear.exam_id = #{examId}::uuid
            AND vas.class_id = #{classId}
            AND ear.result = 'FINISHED'
            AND ear.deleted_at IS NULL
            AND pe.deleted_at IS NULL
    </select>
    
    <!-- 获取试卷的班级正确率 -->
    <select id="getExamCorrectRateByClass" resultType="java.lang.Double">
        SELECT
            AVG(CAST(jsonb_extract_path_text(ear.correct_rate, 'value') AS DECIMAL)) AS correct_rate
        FROM
            exam_analyze_result ear
        JOIN
            view_active_students vas ON ear.student_id = vas.student_id
        WHERE
            ear.exam_id = #{examId}::uuid
            AND vas.class_id = #{classId}
            AND ear.result = 'FINISHED'
            AND ear.deleted_at IS NULL
    </select>
    
    <!-- 获取试卷的班级平均错题量 -->
    <select id="getExamAvgWrongQuestionsByClass" resultType="java.lang.Integer">
        SELECT
            COUNT(*) / COUNT(DISTINCT pe.student_id) AS avg_wrong_questions
        FROM
            personal_exam_question peq
        JOIN
            personal_exam pe ON peq.personal_exam_id = pe.id
        JOIN
            view_active_students vas ON pe.student_id = vas.student_id
        WHERE
            pe.exam_id = #{examId}::uuid
            AND vas.class_id = #{classId}
            AND peq.result = 'mistake'
            AND pe.deleted_at IS NULL
            AND peq.deleted_at IS NULL
    </select>
    
    <!-- 获取试卷的班级薄弱知识点数量 -->
    <select id="getExamWeakKnowledgePointsByClass" resultType="java.lang.Integer">
        SELECT
            COUNT(DISTINCT qkp.knowledge_point_id) AS weak_knowledge_points
        FROM
            question_knowledge_point qkp
        JOIN
            personal_exam_question peq ON qkp.question_id = peq.question_id
        JOIN
            personal_exam pe ON peq.personal_exam_id = pe.id
        JOIN
            view_active_students vas ON pe.student_id = vas.student_id
        WHERE
            pe.exam_id = #{examId}::uuid
            AND vas.class_id = #{classId}
            AND peq.result = 'mistake'
            AND pe.deleted_at IS NULL
            AND peq.deleted_at IS NULL
            AND qkp.deleted_at IS NULL
    </select>
    
    <!-- 获取班级下学生（分页），包含是否有考试结果 -->
    <select id="getClassStudentsWithExamResults" resultType="com.joinus.edu.analytics.model.entity.ClassStudentExamResultEntity">
        SELECT 
            s.student_id as "studentId",
            s.student_name as "studentName",
            s.the_number as "studentNo",
            s.class_name as "className",
            s.grade_name as "gradeName",
            s.class_id,
            s.grade_id,
            e.result as "result",
            e.correct_rate->>'percentile' as "correctRate",
            e.weak_knowledge_points as "weakKnowledgePoints",
            to_char(e.updated_at, 'YYYY-MM-DD HH24:MI:SS') as "reportTime",
            e.id "analyzeId",
            e.personal_exam_id,
            (select count(1) from personal_exam_question peq where peq.personal_exam_id = e.personal_exam_id and result = 'mistake') "errorQuestionCount"
        FROM 
            view_active_students s
        LEFT JOIN 
            (
                SELECT DISTINCT ON (student_id) *
                FROM exam_analyze_result
                WHERE exam_id = #{param.examId}::uuid 
                AND deleted_at IS NULL
                ORDER BY student_id, updated_at DESC
            ) e ON s.student_id = e.student_id
        WHERE 
            s.class_id = #{param.classId}
        ORDER BY 
            CASE WHEN e.id IS NOT NULL THEN 0 ELSE 1 END,
            e.updated_at DESC NULLS LAST,
            s.the_number ASC
    </select>
    <select id="studentInfo" resultType="com.joinus.edu.analytics.model.dto.StudentAnalyticsDto">
        select me.name                                             "examName",
               vas.class_name,
               vas.student_name,
               to_char(ear.updated_at, 'YYYY-MM-DD HH24:MI:SS')  "reportTime",
               round(ces.correct_rate::numeric)::integer                                    "classCorrectRate",
               round(ces.grade_correct_rate::numeric)::integer                                    "grade_correct_rate",
               ear.percentile,
               regexp_replace(ear.correct_rate ->>'percentile', '%', '', 'g')::numeric as personalCorrectRate, ear.exam_id::uuid "examId",
                ces.student_count "examClassStudentCount",
               (select count(1)
                from personal_exam_question peq
                where peq.personal_exam_id = ear.personal_exam_id
                  and peq.result = 'mistake')                      "errorQuestionCount",
               (select count(1)
                from personal_exam_question peq
                where peq.personal_exam_id = ear.personal_exam_id) "allQuestionCount",
               vas.student_img
        from exam_analyze_result ear,
             math_exams me,
             view_active_students vas,
             class_exam_statistics ces
        where ear.id = #{param.analyzeId}
          and me.id = ear.exam_id
          and ces.class_id = #{param.classId}
          and ces.exam_id = ear.exam_id
          and vas.student_id = #{param.studentId}
          and ces.student_count >= 10
    </select>
    <select id="questionList" resultType="com.joinus.edu.analytics.model.entity.PersonalExamQuestion">
        select peq.*
        from exam_analyze_result ear,
             personal_exam_question peq
        where ear.id = #{analyzeId}
          and peq.personal_exam_id = ear.personal_exam_id
    </select>
    
    <!-- u83b7u53d6u6700u8fd1u4e00u4e2au6708u5185u53c2u4e0eu4ebau6570u8d85u8fc7u6307u5b9au503cu7684u8bd5u5377u5217u8868uff08u5206u9875u7248u672cuff09 -->
    <select id="getRecentExamsPage" resultType="com.joinus.edu.analytics.model.dto.RecentExamDTO">
        SELECT 
            ces.id as "classExamId",
            ces.exam_id as "examId",
            me.name as "examName",
            ces.class_id as "classId",
            vas.class_name as "className",
            ces.grade_id as "gradeId",
            vas.grade_name as "gradeName",
            ces.student_count as "studentCount",
            ces.correct_rate as "correctRate",
            ces.created_at as "examTime",
            ces.grade_correct_rate as "gradeCorrectRate",
            (SELECT COUNT(DISTINCT peq.question_id) 
             FROM personal_exam_question peq 
             JOIN personal_exam pe ON peq.personal_exam_id = pe.id 
             WHERE pe.exam_id = ces.exam_id 
             AND peq.result = 'mistake' 
             AND pe.student_id IN (SELECT student_id FROM view_active_students WHERE class_id = ces.class_id)) as "wrongQuestionCount",
            (SELECT COUNT(DISTINCT qkp.knowledge_point_id) 
             FROM question_knowledge_point qkp 
             JOIN class_knowledge_point_statistics ckps ON qkp.knowledge_point_id = ckps.knowledge_point_id 
             WHERE ckps.class_id = ces.class_id 
             AND ckps.exam_id = ces.exam_id 
             AND ckps.correct_rate &lt; 0.6) as "weakKnowledgePointCount"
        FROM 
            class_exam_statistics ces
        JOIN 
            math_exams me ON ces.exam_id = me.id
        JOIN 
            view_active_students vas ON ces.class_id = vas.class_id AND vas.student_id = (SELECT MIN(student_id) FROM view_active_students WHERE class_id = ces.class_id)
        WHERE 
            ces.class_id IN 
            <foreach collection="classIds" item="classId" open="(" separator="," close=")">
                #{classId}
            </foreach>
            AND ces.student_count >= #{studentThreshold}
            AND ces.updated_at >= #{oneMonthAgo}
            AND ces.deleted_at IS NULL
        ORDER BY 
            ces.updated_at DESC
    </select>
    <select id="selectRecentExamAnalytics"
            resultType="com.joinus.edu.analytics.model.result.RecentExamAnalyticsResult">
        select DISTINCT
        on (ear.exam_id) ear.exam_id, me.name "examName", ear.updated_at, ear.correct_rate ->> 'percentile' "correctRate",
            ear.id "analyzeId",
            (select count(1)
            from personal_exam_question peq
            where peq.personal_exam_id = ear.personal_exam_id) "allQuestionCount",
            (select count(1)
            from personal_exam_question peq
            where peq.personal_exam_id = ear.personal_exam_id and peq.result='mistake') "wrongQuestions",
            ear.personal_exam_id
        from exam_analyze_result ear
            inner join math_exams me
        on me.id = ear.exam_id
            inner join class_exam_statistics ces on ces.exam_id = ear.exam_id and ces.student_count>=10
        where ear.student_id = #{param.studentId}
        group by me.name, ear.updated_at, ear.correct_rate, ear.exam_id,ear.id--,peq.personal_exam_id
        order by ear.exam_id, ear.updated_at desc
    </select>

    <!-- 根据学情报告ids查询薄弱知识点列表 -->
    <select id="listWeakKnowledgePointsByExamAnalyzeResultIds"
            resultType="com.joinus.edu.analytics.model.po.MathPersonalExamKnowledgePointPo">
        select pe.id as personalExamId,
               pe.exam_id,
               pe.publisher as examPublisher,
               ear.id as examAnalyzeResultId,
               qkp.knowledge_point_id,
               qkp.publisher as knowledgePointPublisher
        from personal_exam pe
                 inner join exam_analyze_result ear on ear.personal_exam_id = pe.id
                 inner join personal_exam_question peq on peq.personal_exam_id = pe.id
                 inner join question_knowledge_point qkp on qkp.exam_id = ear.exam_id and qkp.question_id = peq.question_id
        where ear.id in
        <foreach item="examAnalyzeResultId" collection="examAnalyzeResultIds" separator="," open="(" close=")">
            #{examAnalyzeResultId}
        </foreach>
    </select>

    <select id="listExamAnalyzeResults"
            resultType="com.joinus.edu.analytics.model.result.ExamAnalyzeResultResult">
        select pe.exam_name as examName,
               ear.id as examAnalyzeResultId
        from personal_exam pe
                 inner join exam_analyze_result ear on pe.id = ear.personal_exam_id
        where ear.result = 'FINISHED'
            and pe.student_id = #{param.studentId}
        order by ear.created_at desc
    </select>
</mapper>