<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.edu.analytics.mapper.PersonalExamQuestionMapper">

    <!-- 根据个人试卷ID统计错题数量 -->
    <select id="countWrongQuestionsByPersonalExamId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM personal_exam_question
        WHERE personal_exam_id = #{personalExamId}
        AND result = 'mistake'
        AND deleted_at IS NULL
    </select>

    <!-- 根据个人试卷ID计算正确率 -->
    <select id="calculateCorrectRateByPersonalExamId" resultType="java.lang.Double">
        SELECT 
            CASE 
                WHEN COUNT(*) = 0 THEN 0
                ELSE CAST(SUM(CASE WHEN result = 'correct' THEN 1 ELSE 0 END) AS DOUBLE PRECISION) / COUNT(*)
            END AS correct_rate
        FROM personal_exam_question
        WHERE personal_exam_id = #{personalExamId}
        AND deleted_at IS NULL
    </select>

</mapper>
