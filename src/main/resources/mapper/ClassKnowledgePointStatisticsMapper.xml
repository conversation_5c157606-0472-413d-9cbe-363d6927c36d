<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.edu.analytics.mapper.ClassKnowledgePointStatisticsMapper">

    <!-- 获取班级薄弱知识点列表 -->
    <select id="getWeakKnowledgePoints" resultType="com.joinus.edu.analytics.model.dto.WeakKnowledgePointDTO">
        SELECT
        qkp.knowledge_point_name as knowledgePointName,
        TRUNC(AVG(ckps.correct_rate))::integer as masteryRate
        FROM
        class_knowledge_point_statistics ckps
        JOIN
        question_knowledge_point qkp ON ckps.knowledge_point_id = qkp.knowledge_point_id
        join class_exam_statistics ces on ces.exam_id = ckps.exam_id and ces.class_id = ckps.class_id and ces.student_count>=10
        WHERE
        ckps.class_id IN
        <foreach collection="classIds" item="classId" open="(" separator="," close=")">
            #{classId}
        </foreach>
        AND ckps.deleted_at IS NULL
        <if test="examId != null">
           AND ckps.exam_id = #{examId}
        </if>
        GROUP BY
        qkp.knowledge_point_name,ckps.correct_rate
        HAVING
        AVG(ckps.correct_rate) &lt; #{threshold}
        ORDER BY
        ckps.correct_rate ASC
        <if test="limit != 0">
            LIMIT #{limit}
        </if>
   </select>
</mapper>
