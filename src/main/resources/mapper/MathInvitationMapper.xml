<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.edu.analytics.mapper.MathInvitationMapper">

    <select id="selectMathInvitationList" resultType="com.joinus.edu.analytics.model.result.MathInvitationPo">
        select vas.school_name as schoolName,
               vas.grade_name as gradeName,
               vas.class_name as className,
               vas.student_name as studentName,
               mi.inviter_student_id as inviterId,
               mi.inviter_phone as telephoneNumber,
               count(mi.*) invitationNum
        from math_invitation mi
                 left join active_students_cache vas on mi.inviter_student_id = vas.student_id
        where 1 = 1
        <if test="vo.schoolId != null and vo.schoolId != ''">
            and vas.school_id = #{vo.schoolId}
        </if>
        <if test="vo.gradeId != null and vo.gradeId != ''">
            and vas.grade_id = #{vo.gradeId}
        </if>
        <if test="vo.classId != null and vo.classId != ''">
            and vas.class_id = #{vo.classId}
        </if>
        <if test="vo.studentName != null and vo.studentName != ''">
            and vas.student_name like '%' || #{vo.studentName} || '%'
        </if>
        <if test="vo.telephoneNumber != null and vo.telephoneNumber != ''">
            and mi.inviter_phone = #{vo.telephoneNumber}
        </if>
        <if test="null != vo.createTimeStart">
            and  <![CDATA[ mi.invited_at >= to_date(#{vo.createTimeStart},'yyyy-mm-dd')]]>
        </if>
        <if test="null != vo.createTimeEnd">
            and  <![CDATA[ mi.invited_at <= to_date(#{vo.createTimeEnd},'yyyy-mm-dd')+1]]>
        </if>
        group by mi.inviter_student_id, vas.school_name, vas.grade_name, vas.class_name, vas.student_name, mi.inviter_phone
    </select>
</mapper>
