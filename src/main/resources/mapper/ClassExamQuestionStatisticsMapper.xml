<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.edu.analytics.mapper.ClassExamQuestionStatisticsMapper">

    <!-- 获取班级平均错题数 -->
    <select id="getAvgWrongQuestionsByClassAndExam" resultType="java.lang.Integer">
        SELECT
            COUNT(*) as avgWrongQuestions
        FROM
            class_exam_question_statistics
        WHERE
            class_id IN
            <foreach collection="classIds" item="classId" open="(" separator="," close=")">
                #{classId}
            </foreach>
            AND exam_id = #{examId}
          and  correct_rate &lt; 100
            AND deleted_at IS NULL
    </select>
    <select id="getClassQuestionStatistics"
            resultType="com.joinus.edu.analytics.model.dto.ClassQuestionStatisticsDto">
        select
        round(ceqs.correct_rate::numeric)::integer "classCorrectRate",
        round(ceqs.grade_correct_rate::numeric)::integer "gradeCorrectRate",
               ceqs.question_id,
               ceqs.sort_no,
               ces.student_count - ceqs.correct_count     "errorStudentCount",
               STRING_AGG(qkp.knowledge_point_name::text, ',') "knowledgePointName"
        from class_exam_question_statistics ceqs
                 left join class_exam_statistics ces on ces.exam_id = ceqs.exam_id and ces.class_id = ceqs.class_id
                 left join question_knowledge_point qkp
                           on qkp.question_id = ceqs.question_id and qkp.exam_id = ceqs.exam_id
        where ceqs.class_id = #{param.classId}
          and ceqs.exam_id = #{param.examId}
        <if test="param.filterType.name == 'WRONG_ONLY'">
               and ceqs.correct_rate &lt; 100.00
        </if>
        group by ceqs.correct_rate, ceqs.grade_correct_rate, ceqs.question_id, ces.student_count, ceqs.correct_count,ceqs.sort_no
        order by ceqs.sort_no
    </select>
    <select id="getStudentQuestionStatistics"
            resultType="com.joinus.edu.analytics.model.dto.ClassQuestionStatisticsDto">
        select
            round(ceqs.correct_rate::numeric)::integer "classCorrectRate",
            round(ceqs.grade_correct_rate::numeric)::integer "gradeCorrectRate",
            ceqs.question_id,
            ceqs.sort_no,
            ces.student_count - ceqs.correct_count     "errorStudentCount",
            (
                SELECT STRING_AGG(qkp_inner.knowledge_point_name::text, ',')
                FROM question_knowledge_point qkp_inner
                WHERE qkp_inner.question_id = ceqs.question_id
                AND qkp_inner.exam_id = #{param.examId}::uuid
            ) "knowledgePointName",
            peq.result
        from personal_exam_question peq
                 inner join class_exam_question_statistics ceqs on ceqs.question_id = peq.question_id
                 left join class_exam_statistics ces on ces.exam_id = ceqs.exam_id and ces.class_id = ceqs.class_id
        where ceqs.class_id = #{param.classId}
          and peq.question_id = ceqs.question_id
        <if test="param.filterType.name == 'WRONG_ONLY'">
            and peq.result = 'mistake'
        </if>
          and peq.personal_exam_id = #{param.personalExamId}
          and ceqs.exam_id = #{param.examId}::uuid
        group by ceqs.correct_rate, ceqs.grade_correct_rate, ceqs.question_id, ces.student_count, ceqs.correct_count, ceqs.sort_no, peq.result, ceqs.exam_id
        order by sort_no
    </select>
</mapper>
