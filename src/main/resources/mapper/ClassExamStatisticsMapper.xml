<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.edu.analytics.mapper.ClassExamStatisticsMapper">

    <select id="getExamDetails" resultType="com.joinus.edu.analytics.model.dto.ExamDetailsDto">
        select distinct vas.class_name                                                                 "className",
                        ces.id,
                        me.name                                                                        "examName",
                        ces.student_count                                                              "examStudentCount",
                        (select count(1) from view_active_students vas where vas.class_id = ces.class_id) "totalStudentCount",
                        vas.grade_id,
                        round(ces.correct_rate::numeric)::integer                                      "correct_rate"
        from class_exam_statistics ces,
             math_exams me,
             view_active_students vas
        where me.id = ces.exam_id
          and vas.class_id = ces.class_id
          and ces.exam_id = #{examId}
          and ces.class_id = #{classId}
    </select>
    <select id="getAllExamClassInfo" resultType="com.joinus.edu.analytics.model.dto.AllExamClassInfoDto">
        select distinct on (class_id) class_id, correct_rate,ces.student_count
        from class_exam_statistics ces
        where ces.class_id in
              (select class_id from view_active_students vas where vas.grade_id = #{gradeId})
            and ces.student_count >= 10
    </select>
    <select id="getExamStatisticsList" resultType="com.joinus.edu.analytics.model.dto.RecentExamDTO">
        select DISTINCT on (ces.exam_id) ces.exam_id, me.name "examName",ces.created_at
        from class_exam_statistics ces,
        math_exams me
        where ces.class_id in
        <foreach collection="classIds" item="classId" open="(" separator="," close=")">
            #{classId}
        </foreach>
        and me.id = ces.exam_id
        and ces.student_count >= 10
        order by ces.exam_id,ces.created_at desc
    </select>
    
    <!-- 获取一个月内的不重复考试ID列表 -->
    <select id="getDistinctExamIds" resultType="java.util.UUID">
        select distinct exam_id
        from class_exam_statistics
        where class_id in
        <foreach collection="classIds" item="classId" open="(" separator="," close=")">
            #{classId}
        </foreach>
        and updated_at >= #{oneMonthAgo}
        order by exam_id desc
    </select>
    
    <!-- 根据考试ID列表获取班级考试统计数据 -->
    <select id="getExamStatisticsByExamIds" resultType="com.joinus.edu.analytics.model.entity.ClassExamStatistics">
        select
            ces.id,
            ces.class_id,
            ces.grade_id,
            ces.exam_id,
            ces.correct_rate,
            ces.student_count,
            ces.created_at,
            ces.updated_at,
            ces.deleted_at
        from class_exam_statistics ces
        where ces.class_id in
        <foreach collection="classIds" item="classId" open="(" separator="," close=")">
            #{classId}
        </foreach>
        and ces.exam_id in
        <foreach collection="examIds" item="examId" open="(" separator="," close=")">
            #{examId}
        </foreach>
        and ces.updated_at >= #{oneMonthAgo}
    </select>

</mapper>
