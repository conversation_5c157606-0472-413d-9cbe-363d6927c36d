<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.edu.analytics.mapper.KnowledgePointMapper">

    <!-- 获取参与考试的学生数量 -->
    <select id="countStudentsByClassIds" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT ear.student_id) AS count
        FROM exam_analyze_result ear
        JOIN view_active_students vas ON ear.student_id = vas.student_id
        WHERE vas.class_id IN
        <foreach collection="classIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        AND ear.result = 'FINISHED'
        AND ear.deleted_at IS NULL
    </select>

    <!-- 获取知识点统计数据 -->
    <select id="getKnowledgePointStats" resultType="java.util.HashMap">
        SELECT
        qkp.knowledge_point_id AS "knowledgePointId",
        qkp.knowledge_point_name AS "knowledgePointName",
        qkp.exam_id AS examId,
        COUNT(DISTINCT peq.question_id) AS "totalCount",
        SUM(CASE WHEN peq.result = 'correct' THEN 1 ELSE 0 END) AS "correctCount"
        FROM question_knowledge_point qkp
        JOIN personal_exam_question peq ON qkp.question_id = peq.question_id
        JOIN personal_exam pe ON peq.personal_exam_id = pe.id
        JOIN exam_analyze_result ear ON pe.id = ear.personal_exam_id
        JOIN view_active_students vas ON ear.student_id = vas.student_id
        WHERE
        vas.class_id IN
        <foreach collection="classIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        AND ear.result = 'FINISHED'
        AND qkp.deleted_at IS NULL
        AND peq.deleted_at IS NULL
        AND pe.deleted_at IS NULL
        AND ear.deleted_at IS NULL
        <!-- 仅考虑每个学生每个考试的最新一次尝试 -->
        AND ear.id IN (
        SELECT MAX(ear2.id)
        FROM exam_analyze_result ear2
        WHERE ear2.student_id = ear.student_id
        AND ear2.exam_id = ear.exam_id
        AND ear2.result = 'FINISHED'
        AND ear2.deleted_at IS NULL
        GROUP BY ear2.student_id, ear2.exam_id
        )
        GROUP BY
        qkp.knowledge_point_id, qkp.knowledge_point_name, qkp.exam_id
    </select>
    <select id="getKnowledgePointAnalysis"
            resultType="com.joinus.edu.analytics.model.result.ClassKnowledgePointAnalyticsResult">
        SELECT DISTINCT ckps.correct_rate                                                               "classCorrectRate",
                        ckps.knowledge_point_id,
                        ces.exam_id,
                        qkp.knowledge_point_name,
                        ckps.grade_correct_rate,
                        STRING_AGG(ceqs.sort_no::text, ',') OVER (PARTITION BY ckps.knowledge_point_id) "questionSortNo"
        FROM class_exam_statistics ces
                 JOIN class_knowledge_point_statistics ckps ON ces.id = ckps.class_exam_id
                 JOIN question_knowledge_point qkp ON qkp.knowledge_point_id = ckps.knowledge_point_id
                 JOIN class_exam_question_statistics ceqs ON ceqs.question_id = qkp.question_id
            AND ceqs.class_id = ces.class_id
        WHERE ces.exam_id = #{param.examId}
          AND qkp.exam_id = ces.exam_id
          AND ces.class_id = #{param.classId}
        ORDER BY ckps.correct_rate
    </select>

    <select id="getStudentKnowledgePointAnalysis"
            resultType="com.joinus.edu.analytics.model.result.StudentKnowledgePointAnalyticsDto">
        WITH latest_personal_exam AS (SELECT DISTINCT ON (student_id, exam_id) id, student_id, exam_id
                                      FROM personal_exam
                                      WHERE student_id = #{param.studentId}
                                        AND exam_id = #{param.examId}
                                        AND deleted_at IS NULL
                                      ORDER BY student_id, exam_id, updated_at DESC),
             knowledge_points AS (SELECT DISTINCT ON (qkp.knowledge_point_id) qkp.knowledge_point_name,
                                                                              qkp.knowledge_point_id,
                                                                              qkp.question_id,
                                                                              lpe.id AS personal_exam_id,
                                                                              lpe.exam_id
                                  FROM latest_personal_exam lpe
                                           JOIN question_knowledge_point qkp ON qkp.exam_id = lpe.exam_id
                                  WHERE qkp.deleted_at IS NULL
                                  ORDER BY qkp.knowledge_point_id)
        SELECT kp.knowledge_point_name AS "knowledgePointName",
               kp.knowledge_point_id   AS "knowledgePointId",
               kp.question_id          AS "questionId",
               kp.personal_exam_id     AS "personalExamId",
               kp.exam_id              AS "examId"
        FROM knowledge_points kp
        ORDER BY kp.knowledge_point_id
    </select>

    <!-- u4e00u6b21u6027u83b7u53d6u5b66u751fu77e5u8bc6u70b9u5206u6790u6240u9700u7684u6240u6709u6570u636euff0cu5305u542bu77e5u8bc6u70b9u3001u9898u76eeu3001u6b63u786eu7387u7b49u4fe1u606f -->
    <select id="getStudentKnowledgePointAnalysisDetail"
            resultType="com.joinus.edu.analytics.model.result.StudentKnowledgePointAnalyticsDto">
        WITH knowledge_points AS (
        SELECT DISTINCT ON (qkp.knowledge_point_id)
        qkp.knowledge_point_name,
        qkp.knowledge_point_id,
        pe.id AS personal_exam_id,
        pe.exam_id
        FROM personal_exam pe
        JOIN question_knowledge_point qkp ON qkp.exam_id = pe.exam_id
        WHERE pe.student_id = #{param.studentId}
        AND pe.exam_id = #{param.examId}
        AND pe.deleted_at IS NULL
        AND qkp.deleted_at IS NULL
        ORDER BY qkp.knowledge_point_id
        ),
        question_stats AS (
        SELECT
        qkp.knowledge_point_id,
        peq.sort_no,
        peq.result,
        COUNT(*) OVER (PARTITION BY qkp.knowledge_point_id) AS total_questions,
        SUM(CASE WHEN peq.result = 'correct' THEN 1 ELSE 0 END) OVER (PARTITION BY qkp.knowledge_point_id) AS
        correct_questions
        FROM knowledge_points kp
        JOIN question_knowledge_point qkp ON qkp.knowledge_point_id = kp.knowledge_point_id AND qkp.exam_id = kp.exam_id
        JOIN personal_exam_question peq ON peq.question_id = qkp.question_id AND peq.personal_exam_id =
        kp.personal_exam_id
        WHERE qkp.deleted_at IS NULL
        AND peq.deleted_at IS NULL
        ),
        class_stats AS (
        SELECT
        ckps.knowledge_point_id,
        ckps.correct_rate,
        ckps.grade_correct_rate
        FROM class_knowledge_point_statistics ckps
        WHERE ckps.exam_id = #{param.examId}
        AND ckps.class_id = #{param.classId}
        AND ckps.deleted_at IS NULL
        ),
        aggregated_data AS (
        SELECT
        kp.knowledge_point_id,
        kp.knowledge_point_name,
        kp.personal_exam_id AS "personalExamId",
        kp.exam_id,
        CASE
        WHEN MAX(qs.total_questions) > 0 THEN
        ROUND((MAX(qs.correct_questions) * 100.0 / MAX(qs.total_questions)), 0)
        ELSE 0
        END AS mastery_level,
        array_to_string(array_agg(DISTINCT qs.sort_no), ',') AS question_sort_nos,
        ROUND(MAX(cs.correct_rate), 0) AS class_correct_rate,
        ROUND(MAX(cs.grade_correct_rate), 0) AS grade_correct_rate
        FROM knowledge_points kp
        LEFT JOIN question_stats qs ON qs.knowledge_point_id = kp.knowledge_point_id
        LEFT JOIN class_stats cs ON cs.knowledge_point_id = kp.knowledge_point_id
        GROUP BY kp.knowledge_point_id, kp.knowledge_point_name, kp.personal_exam_id, kp.exam_id
        )
        SELECT
        knowledge_point_id AS "knowledgePointId",
        knowledge_point_name AS "knowledgePointName",
        "personalExamId",
        exam_id AS "examId",
        COALESCE(mastery_level, 0) AS "masteryLevel",
        question_sort_nos AS "questionSortNoList",
        COALESCE(class_correct_rate, 0) AS "classCorrectRate",
        COALESCE(grade_correct_rate, 0) AS "gradeCorrectRate"
        FROM aggregated_data
        WHERE 1=1
        <if test="param.isWeak != null and param.isWeak">
            AND mastery_level &lt;= 60
        </if>
        ORDER BY mastery_level
    </select>
    <select id="getStudentKnowledgePointDetail"
            resultType="com.joinus.edu.analytics.model.result.StudentKnowledgePointAnalyticsDto">
        select
            distinct on (qkp.knowledge_point_id)
            pe.id                                   "personalExamId",
            qkp.knowledge_point_name,
            qkp.question_id,
            qkp.knowledge_point_id,
            COALESCE(ceqs.correct_rate, 0)       AS "classCorrectRate",
            COALESCE(ceqs.grade_correct_rate, 0) AS "gradeCorrectRate"
        from question_knowledge_point qkp
                 join class_knowledge_point_statistics ceqs
                      on ceqs.exam_id = #{param.examId} and ceqs.class_id = #{param.classId}
                          and ceqs.knowledge_point_id = qkp.knowledge_point_id
                 join (
            select *
            from personal_exam
            where exam_id = #{param.examId} and student_id = #{param.studentId}
            order by id desc
            limit 1
        ) pe on true
                 left join personal_exam_question peq on peq.personal_exam_id = pe.id and peq.question_id = qkp.question_id
        where qkp.exam_id = #{param.examId}
          and qkp.exam_id is not null
        order by qkp.knowledge_point_id, qkp.question_id
    </select>
</mapper>
