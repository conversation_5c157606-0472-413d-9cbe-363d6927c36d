<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.edu.analytics.mapper.MathExamsMapper">

    <!-- 根据试卷ID列表获取试卷信息 -->
    <select id="getExamsByIds" resultType="com.joinus.edu.analytics.model.entity.MathExams">
        SELECT
            id, name, semester, grade, created_at as createdAt, updated_at as updatedAt
        FROM
            math_exams
        WHERE
            id IN
            <foreach collection="examIds" item="examId" open="(" separator="," close=")">
                #{examId}
            </foreach>
            AND deleted_at IS NULL
    </select>
</mapper>
