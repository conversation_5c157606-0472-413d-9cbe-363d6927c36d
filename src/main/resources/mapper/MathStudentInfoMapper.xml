<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.edu.analytics.mapper.MathStudentInfoMapper">

    <select id="getStudentPlanPage" resultType="com.joinus.edu.analytics.model.dto.StudentInfoPlanDto">
        select
            msi.grade,
            msi.student_id,
            v.student_name,
            v.school_name,
            v.school_id,
            msi.study_plan_enabled
        from math_student_info msi
                 INNER JOIN view_active_students v on v.student_id = msi.student_id
        where msi.deleted_at is null
        <if test="param.studentName != null">
            and v.student_name like concat('%', #{param.studentName}, '%')
        </if>
        <if test="param.schoolId != null">
            and v.school_id = #{param.schoolId}
        </if>
        <if test="param.studentIds != null">
            and msi.student_id in
            <foreach item="item" index="index" collection="param.studentIds"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by msi.student_id
    </select>
    <update id="updateStudentPlanStatus">
        update math_student_info
        set study_plan_enabled = #{studyPlanEnabled},
            updated_at = now()
        where student_id = #{studentId}
    </update>
</mapper>
