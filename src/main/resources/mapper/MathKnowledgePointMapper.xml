<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.edu.analytics.mapper.MathKnowledgePointMapper">


    <select id="listByIds" resultType="com.joinus.edu.analytics.model.po.MathKnowledgePointPo">
        select  mkp.id,
                mkp.name,
                mkp.sort_no,
                v.section_id as nodeId
        from math_knowledge_points mkp
        inner join view_math_knowledge_points v on v.knowledge_point_id = mkp.id
        where v.knowledge_point_id in
            <foreach item="item" index="index" collection="knowledgePointIds" separator="," open="(" close=")">
                #{item}
            </foreach>
    </select>
</mapper>
