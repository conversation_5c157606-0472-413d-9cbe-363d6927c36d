server:
  port: 9103
  servlet:
    context-path: /edu-analytics
    
# 转发服务配置
forwarding:
  # 目标服务的URL
  target-service-url: http://127.0.0.1:8080
spring:
  application:
    name: edu-analytics-manager

  # PostgreSQL数据库配置
  datasource:
    driver-class-name: org.postgresql.Driver
    url: jdbc:postgresql://**************:5432/smart_study_analytics
    username: smart_study_analytics
    password: qF9hI4bN3y
    # 数据库连接池配置
    hikari:
      # 连接池名称
      pool-name: HikariCP-Pool
      # 最小空闲连接数
      minimum-idle: 10
      # 最大连接数
      maximum-pool-size: 50
      # 空闲连接超时时间（毫秒）
      idle-timeout: 600000
      # 连接最大生命周期（毫秒）
      max-lifetime: 1800000
      # 连接超时时间（毫秒）
      connection-timeout: 30000
      # 测试连接是否有效的SQL语句
      connection-test-query: SELECT 1
      # 自动提交
      auto-commit: true
  
  # Redis配置
  data:
    redis:
      host: **************
      port: 6379
      # 数据库索引
      database: 0
      # 连接超时时间（毫秒）
      timeout: 10000
      # 连接池配置
      lettuce:
        pool:
          # 最大连接数
          max-active: 32
          # 最大空闲连接
          max-idle: 16
          # 最小空闲连接
          min-idle: 8
          # 连接等待超时时间（毫秒）
          max-wait: 3000
        shutdown-timeout: 200ms
  
  # 解决Springfox与Spring Boot 3.x的兼容问题
  mvc:
    view:
      prefix: /WEB-INF/
      suffix: .jsp

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.joinus.edu.analytics.entity
  # 类型处理器
  type-handlers-package: com.joinus.edu.analytics.config.typehandler
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 处理JDBC类型与Java类型的映射
    default-enum-type-handler: org.apache.ibatis.type.EnumTypeHandler
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deletedAt
      logic-delete-value: NOW()
      logic-not-delete-value: NULL

# Knife4j配置
knife4j:
  enable: true
  setting:
    language: zh-CN
    enable-swagger-models: true
    enable-document-manage: true
    swagger-model-name: 实体类列表
    enable-version: false

# SpringDoc配置
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
  packages-to-scan: com.joinus.edu.analytics.controller
  paths-to-match: /**

apollo:
  bootstrap:
    enabled: true
    namespaces: application
    eagerLoad:
      enabled: true

management:
  endpoints:
    web:
      exposure:
        # 暴露更多端点
        include: health,info,metrics,prometheus
      base-path: /actuator
  endpoint:
    health:
      show-details: always
      show-components: always
      probes:
        enabled: true
      group:
        readiness:
          include: db,redis,diskSpace
        liveness:
          include: ping
  info:
    env:
      enabled: true
  health:
    db:
      enabled: true
    redis:
      enabled: true
  metrics:
    tags:
      application: ${spring.application.name}