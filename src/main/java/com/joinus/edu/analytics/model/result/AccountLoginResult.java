package com.joinus.edu.analytics.model.result;

import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class AccountLoginResult {

    private int code;
    private String msg;
    private DataDTO data;

    @NoArgsConstructor
    @Data
    public static class DataDTO {
        private Long id;
        private Object address;
        private int buka;
        private Object cardcode;
        private Object identity;
        private String loginIp;
        private String loginTime;
        private int num;
        private String password;
        private int sex;
        private Object subjectids;
        private Object subjectnames;
        private String teacherName;
        private String telNum;
        private int schoolId;
        private int departmentId;
        private int typeId;
        private int nationId;
        private int isVirtualAccount;
        private Object subjectId;
        private String createTime;
        private Object cookie;
        private Object confirmcode;
        private Object confirmtime;
        private Object teacherCode;
        private int roleId;
        private Object teacherImg;
        private Object email;
        private String isEmchat;
        private String isPrincipal;
        private Object introduction;
        private Object headUploadTime;
        private Object creator;
        private String isDisplay;
        private Object oaPersonId;
        private Object oaPostId;
        private int appLoginSms;
        private String updateTime;
        private Object wxName;
        private Object openid;
        private Object unionId;
        private Object qqNickName;
        private Object qqOpenid;
        private Object thelead;
        private Object afterlead;
        private String imei;
        private int appTimes;
        private int viewCreditMall;
        private Object wechatUserId;
        private String birthday;
        private Object region;
        private Object wxVsOpenid;
        private int reserveIsDefault;
        private int leaveIsDefault;
        private String wxVsPassword;
        private int isJKai;
        private String deviceUniqueId;
        private int birthdayType;
        private Object chineseBirthday;
        private Object teacherTitle;
        private int isactive;
        private String passwordBcrypt;
        private Object wuid;
    }
}
