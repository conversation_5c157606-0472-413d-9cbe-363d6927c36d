package com.joinus.edu.analytics.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import com.joinus.edu.analytics.model.enums.PublisherEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数学学生信息表
 * @TableName math_student_info
 */
@TableName(value ="math_student_info")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MathStudentInfo implements Serializable {
    /**
     * 学生id
     */
    private Long studentId;

    /**
     * 年级
     */
    private Integer grade;

    /**
     * 教材版本
     */
    private PublisherEnum publisher;

    /*
     * 是否开启学习计划
     */
    private Boolean studyPlanEnabled;

    /**
     * 
     */
    private Date createdAt;

    /**
     * 
     */
    private Date updatedAt;

    /**
     * 
     */
    private Date deletedAt;
}