package com.joinus.edu.analytics.model.result;

import cn.hutool.core.collection.CollUtil;
import com.joinus.edu.analytics.model.dto.MathCatalogNodeDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MathCatalogNodeResult implements Serializable {

    @Schema(description = "目录节点id")
    private UUID id;

    @Schema(description = "目录节点父id")
    private UUID parentId;

    @Schema(description = "目录节点名称前缀")
    private String namePrefix;

    @Schema(description = "目录节点名称")
    private String name;

    @Schema(description = "目录节点层级")
    private Integer level;

    @Schema(description = "目录节点排序")
    private Integer sortNo;

    @Schema(description = "目录节点是否叶子节点")
    private Boolean isLeaf;

    @Schema(description = "目录子节点")
    private List<MathCatalogNodeResult> subNodes;

    @Schema(description = "节点下面的知识点")
    private List<MathKnowledgePointResult> knowledgePoints;

    public static MathCatalogNodeResult ofMathCatalogNodeDto(MathCatalogNodeDto mathCatalogNodeDto) {
        MathCatalogNodeResult result = MathCatalogNodeResult.builder()
                .id(mathCatalogNodeDto.getId())
                .name(mathCatalogNodeDto.getName())
                .level(mathCatalogNodeDto.getLevel())
                .sortNo(mathCatalogNodeDto.getSortNo())
                .parentId(mathCatalogNodeDto.getParentId())
                .knowledgePoints(List.of())
                .namePrefix(mathCatalogNodeDto.getNamePrefix())
                .isLeaf(CollUtil.isEmpty(mathCatalogNodeDto.getSubNodes()))
                .build();

        // 递归转换所有子节点
        if (CollUtil.isNotEmpty(mathCatalogNodeDto.getSubNodes())) {
            List<MathCatalogNodeResult> subResults = mathCatalogNodeDto.getSubNodes()
                    .stream()
                    .map(MathCatalogNodeResult::ofMathCatalogNodeDto)
                    .toList();
            result.setSubNodes(subResults);
        } else {
            result.setSubNodes(List.of());
        }
        if (CollUtil.isNotEmpty(mathCatalogNodeDto.getKnowledgePoints())) {
            List<MathKnowledgePointResult> knowledgePointResults = mathCatalogNodeDto.getKnowledgePoints()
                    .stream()
                    .map(MathKnowledgePointResult::ofMathKnowledgePointDto)
                    .toList();
            result.setKnowledgePoints(knowledgePointResults);
        } else {
            result.setKnowledgePoints(List.of());
        }
        return result;
    }

}
