package com.joinus.edu.analytics.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 班级试卷题目分析实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("class_exam_question")
public class ClassExamQuestion {
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 班级ID
     */
    private Long classId;
    
    /**
     * 年级ID
     */
    private Long gradeId;
    
    /**
     * 试卷ID
     */
    private UUID examId;
    
    /**
     * 题目ID
     */
    private UUID questionId;
    
    /**
     * 错误率
     */
    private BigDecimal errorRate;
    
    /**
     * 错误人数
     */
    private Integer errorCount;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 逻辑删除
     */
    private LocalDateTime deletedAt;
}
