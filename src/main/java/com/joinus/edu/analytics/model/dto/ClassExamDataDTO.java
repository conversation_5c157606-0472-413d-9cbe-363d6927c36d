package com.joinus.edu.analytics.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 班级考试数据DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "班级考试数据DTO")
public class ClassExamDataDTO {
    
    @Schema(description = "班级ID", example = "1001")
    private Long classId;
    
    @Schema(description = "班级名称", example = "高一(1)班")
    private String className;
    
    @Schema(description = "班级总人数", example = "45")
    private Integer totalStudents;
    
    @Schema(description = "参与考试人数", example = "42")
    private Integer participatedStudents;
    
    @Schema(description = "班级正确率", example = "0.78")
    private Long correctRate;
    
    @Schema(description = "班级平均错题量", example = "5")
    private Integer avgWrongQuestions;
    
    @Schema(description = "薄弱知识点数量", example = "3")
    private Integer weakKnowledgePoints;
}
