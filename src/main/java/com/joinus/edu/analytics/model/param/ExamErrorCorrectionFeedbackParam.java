package com.joinus.edu.analytics.model.param;

import lombok.*;

import java.util.Date;

/**
 * 试卷纠错后台列表参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class ExamErrorCorrectionFeedbackParam extends PageParam {
    /**
     * 姓名
     */
    private String parentName;
    /**
     * 电话
     */
    private String tel;
    /**
     * 试卷名称
     */
    private String examName;
    /**
     * 反馈时间
     */
    private Date createTimeStart;
    /**
     * 反馈时间
     */
    private Date createTimeEnd;
    /**
     * 状态
     */
    private String result;
    /**
     * 处理人
     */
    private String handlerName;
    /**
     * 反馈类型
     */
    private String feedbackType;

    private String examId;
}
