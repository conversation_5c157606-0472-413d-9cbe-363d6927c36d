package com.joinus.edu.analytics.model.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.UUID;

/**
 * u5b66u751fu6700u8fd1u8003u60c5u5206u6790u8bb0u5f55u7ed3u679c
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RecentExamAnalyticsResult {
    /**
     * u8bd5u5377u540du79f0
     */
    private String examName;
    
    /**
     * u8003u60c5u5206u6790u751fu6210u65f6u95f4
     */
    private String updatedAt;
    
    /**
     * u6b63u786eu7387
     */
    private String correctRate;
    
    /**
     * u9519u9898u91cf
     */
    private Integer wrongQuestions;
    private UUID examId;
    private Long analyzeId;
    private Long personalExamId;
}
