package com.joinus.edu.analytics.model.enums;

/**
 * personal_exam_question表questionType枚举
 */
public enum QuestionTypeEnum {
    MULTIPLE_CHOICE("选择题"),
    FILL_BLANK("填空题"),
    FREE_RESPONSE("解答题"),
    TRUE_FALSE("判断题"),
    PROOF("证明题"),
    APPLICATION("应用题"),
    CALCULATION("计算题"),
    OTHER("其他"),
    FILL_IN_THE_BLANK("填空题"),
    PROBLEM_SOLVING("解答题");
    private String desc;
    QuestionTypeEnum(String desc) {
        this.desc = desc;
    }

    //根据desc获取name
    public static QuestionTypeEnum getEnumByDesc(String desc) {
        for (QuestionTypeEnum questionTypeEnum : QuestionTypeEnum.values()) {
            if (questionTypeEnum.getDesc().equals(desc)) {
                return questionTypeEnum;
            }
        }
        return QuestionTypeEnum.OTHER;
    }
    
    /**
     * 获取所有题型枚举值
     * 
     * @return 所有题型的枚举数组
     */
    public static QuestionTypeEnum[] getAllTypes() {
        return QuestionTypeEnum.values();
    }
    
    public String getDesc() {
        return desc;
    }
}
