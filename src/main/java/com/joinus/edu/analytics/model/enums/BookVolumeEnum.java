package com.joinus.edu.analytics.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Slf4j
public enum BookVolumeEnum {
    UPPER("上册",  1),
    MIDDLE("中册",  3),
    LOWER("下册",  2);

    private String desc;
    private Integer volumeNum;

    public static String getBookVolumeDesc(Integer volumeNum) {
        for (BookVolumeEnum bookVolume : BookVolumeEnum.values()) {
            if (bookVolume.getVolumeNum().equals(volumeNum)) {
                return bookVolume.desc;
            }
        }
        return "未知";
    }

    public static BookVolumeEnum fromVolumeNum(Integer volumeNum) {
        for (BookVolumeEnum volume : BookVolumeEnum.values()) {
            if (volume.getVolumeNum().equals(volumeNum)) {
                return volume;
            }
        }
        return BookVolumeEnum.LOWER;
    }
}
