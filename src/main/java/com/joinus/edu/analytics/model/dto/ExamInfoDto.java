package com.joinus.edu.analytics.model.dto;

import com.joinus.edu.analytics.model.enums.QuestionDifficultyType;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.UUID;

@Data
@Builder
public class ExamInfoDto {

    private UUID examId;
    private String questionId;
    private String examName;
    private String questionType;
    private String questionContent;
    private Integer sortNo;
    private Integer difficulty;
    private String answer;
    private String analysis;
    private String ossUrl;
    private String knowledgePointNames;

}
