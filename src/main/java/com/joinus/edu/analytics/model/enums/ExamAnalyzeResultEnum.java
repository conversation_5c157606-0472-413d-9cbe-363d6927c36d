package com.joinus.edu.analytics.model.enums;

/**
 * 考情分析结果状态枚举
 */
public enum ExamAnalyzeResultEnum {

    PENDING("未处理"),
    IN_ERROR("纠错中"),
    FINISHED_ERROR("纠错完成"),
    IN_PROGRESS("处理中"),
    FINISHED("完成"),
    INVALID("无效");

    private final String description;

    ExamAnalyzeResultEnum(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}
