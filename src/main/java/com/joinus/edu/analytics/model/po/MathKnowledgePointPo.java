package com.joinus.edu.analytics.model.po;

import com.joinus.edu.analytics.model.entity.MathKnowledgePoint;
import com.joinus.edu.analytics.model.enums.PublisherEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
public class MathKnowledgePointPo extends MathKnowledgePoint {

    private UUID nodeId;
}
