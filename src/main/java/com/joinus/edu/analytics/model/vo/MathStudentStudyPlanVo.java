package com.joinus.edu.analytics.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description :学生学习计划vo
 * <AUTHOR>
 * @Date 2025/9/19 14:54
 **/
@Data
@Builder
public class MathStudentStudyPlanVo implements Serializable {
    @Schema(description = "学生ID")
    private Long studentId;
    @Schema(description = "周号")
    private Integer weekNo;
    @Schema(description = "年级名称")
    private String gradeName;
    private List<MathKnowledgePointVO> planKnowledgePoint;
}
