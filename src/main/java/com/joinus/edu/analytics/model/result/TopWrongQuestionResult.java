package com.joinus.edu.analytics.model.result;

import com.joinus.edu.analytics.model.enums.QuestionTypeEnum;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Set;
import java.util.UUID;

@Data
@Builder
public class TopWrongQuestionResult {

    private double errorRate;
    private int errorStudentCount;
    private String questionType;
    private Set<String> knowledgePoints;
    private UUID questionId;
    private Integer sortNo;
}
