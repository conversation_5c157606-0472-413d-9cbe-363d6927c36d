package com.joinus.edu.analytics.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.UUID;

@Data
public class StudentInfoParam extends PageParam{

    @Schema(description = "考试分析报告ID")
    private Long analyzeId;
    @Schema(description = "学生ID")
    private Long studentId;
    private Long classId;
    private Long gradeId;
    @Schema(description = "学生名称")
    private String studentName;
    @Schema(description = "手机号码")
    private String parentTelNum;
    @Schema(description = "是否开启学习计划")
    private Boolean studyPlanEnabled;
    @Schema(description = "试卷名称")
    private String examName;
    @Schema(description = "学生IDs",hidden = true)
    private List<Long> studentIds;
    @Schema(description = "家长姓名",hidden = true)
    private String parentName;

}
