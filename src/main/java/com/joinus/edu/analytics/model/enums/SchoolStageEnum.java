package com.joinus.edu.analytics.model.enums;

import cn.hutool.core.collection.CollUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

@Getter
@AllArgsConstructor
public enum SchoolStageEnum {
    PRIMARY( "小学", 1, CollUtil.toList(PublisherEnum.REN_JIAO, PublisherEnum.SU_JIAO)),
    JUNIOR("初中", 2, CollUtil.toList(PublisherEnum.REN_JIAO, PublisherEnum.BEI_SHI_DA, PublisherEnum.HUA_SHI_DA)),
    SENIOR( "高中", 3, CollUtil.toList(PublisherEnum.REN_JIAO_A));


    private final String desc;

    private int sortNo;

    private List<PublisherEnum> publishers;

}
