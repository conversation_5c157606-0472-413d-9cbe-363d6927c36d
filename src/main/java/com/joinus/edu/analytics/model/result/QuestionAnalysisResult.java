package com.joinus.edu.analytics.model.result;

import lombok.Builder;
import lombok.Data;

import java.util.Set;
import java.util.UUID;

/**
 * 题目分析结果
 */
@Data
@Builder
public class QuestionAnalysisResult {

    /**
     * 题目ID
     */
    private UUID questionId;
    
    /**
     * 题目类型
     */
    private String questionType;
    
    /**
     * 知识点集合
     */
    private Set<String> knowledgePoints;
    
    /**
     * 错误人数
     */
    private Integer errorStudentCount;
    
    /**
     * 题目正确率
     */
    private Double correctRate;
    
    /**
     * 年级平均正确率
     */
    private Double gradeAvgCorrectRate;
    /**
     * 个人答题状态
     */
    private String answerState;
}
