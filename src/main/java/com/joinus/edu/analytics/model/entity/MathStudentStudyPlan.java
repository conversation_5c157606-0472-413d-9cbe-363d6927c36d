package com.joinus.edu.analytics.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.UUID;

/**
 * 学生学习计划
 * @TableName math_student_study_plan
 */
@TableName(value ="math_student_study_plan")
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class MathStudentStudyPlan extends BaseEntity {

    /**
     * 学生ID
     */
    @TableField("student_id")
    private Long studentId;

    /**
     * 知识点ID
     */
    @TableField("knowledge_point_id")
    private UUID knowledgePointId;

    /**
     * 周数
     */
    @TableField("week_no")
    private Integer weekNo;

    /**
     * 周内排序号
     */
    @TableField("sort_no")
    private Integer sortNo;

}