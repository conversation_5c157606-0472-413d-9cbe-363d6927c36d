package com.joinus.edu.analytics.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 数学邀请记录表
 * @TableName math_invitation
 */
@TableName(value ="math_invitation")
@Data
public class MathInvitation implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 邀请者的用户 ID
     */
    private Long inviterId;

    /**
     * 被邀请者的用户 ID
     */
    private Long inviteeId;

    /**
     * 邀请时间
     */
    private Date invitedAt;

    /**
     * 邀请链路径，例如A.B.C
     */
    private Object path;

    /**
     * 类型: HOLIDAY_TRAINING
     */
    private String type;

}
