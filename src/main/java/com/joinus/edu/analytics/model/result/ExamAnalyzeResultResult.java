package com.joinus.edu.analytics.model.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExamAnalyzeResultResult implements Serializable {

    @Schema(description = "考情分析报告id")
    private Long examAnalyzeResultId;
    @Schema(description = "试卷名称")
    private String examName;

}
