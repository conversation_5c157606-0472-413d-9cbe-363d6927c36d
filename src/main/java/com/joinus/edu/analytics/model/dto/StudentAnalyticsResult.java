package com.joinus.edu.analytics.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StudentAnalyticsResult {

    private String examName;
    private String className;
    private String studentName;
    private String reportTime;
    private String classCorrectRate;
    private String gradeCorrectRate;
    private Integer errorQuestionCount;
    private Integer allQuestionCount;
    private Integer studentCount;
    private Double percentile;
    private UUID examId;
    private Integer classRanking;
    private Integer gradeRanking;
    private String personalCorrectRate;
    private String resultOverview;
    private Long examGradeStudentCount;
    private Long examClassStudentCount;
    private String studentImg;
}
