package com.joinus.edu.analytics.model.dto;

import com.joinus.edu.analytics.model.result.ClassInfoDataDTO;
import com.joinus.edu.analytics.model.result.ClassInfoListDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 年级班级分组DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GradeClassGroupDTO {
    /**
     * 年级ID
     */
    private Long gradeId;
    
    /**
     * 年级名称
     */
    private String gradeName;
    
    /**
     * 班级列表
     */
    private List<GradeClassListDTO> classList;
}
