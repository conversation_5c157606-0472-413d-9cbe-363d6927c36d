package com.joinus.edu.analytics.model.result;

import com.joinus.edu.analytics.model.enums.BookVolumeEnum;
import com.joinus.edu.analytics.model.enums.PublisherEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MathBookNodeResult implements Serializable {

    @Schema(description = "教材版本")
    private PublisherEnum publisher;

    @Schema(description = "年级")
    private Integer grade;

    @Schema(description = "册别")
    private BookVolumeEnum bookVolume;

    @Schema(description = "目录节点")
    private List<MathCatalogNodeResult> subNodes;

}
