package com.joinus.edu.analytics.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Slf4j
public enum PublisherEnum {
    BEI_SHI_DA("北师大版",  "BEI_SHI_DA", 2),
    HUA_SHI_DA("华师大版", "HUA_SHI_DA", 3),
    REN_JIAO("人教版", "REN_JIAO", 1),
    SU_JIAO("苏教版", "SU_JIAO", 4),
    REN_JIAO_A("人教A版", "REN_JIAO_A", 5);

    private String description;
    @EnumValue
    private String value;
    private Integer sortNo;

    /*
     * hutool json反序列化时会默认调用这个方法，不清楚为什么
     */
    public static PublisherEnum ofCustomerName(String name) {
        List<String> nameList = Arrays.stream(PublisherEnum.values()).map(PublisherEnum::name).collect(Collectors.toList());
        if (nameList.contains(name)) {
            return PublisherEnum.valueOf(name);
        }
        if (!name.endsWith("版")) {
            name = name + "版";
        }
        for (PublisherEnum publisherEnum : PublisherEnum.values()) {
            if (publisherEnum.getDescription().equals(name)) {
                return publisherEnum;
            }
        }
        log.error("未知的出版社：{}", name);
        return BEI_SHI_DA;
    }

    public static String getPublisherName(String value) {
        if (value != null && value.contains("版")){
            return value;
        }
        for (PublisherEnum publisherEnum : PublisherEnum.values()) {
            if (publisherEnum.getValue().equals(value)) {
                return publisherEnum.getDescription();
            }
        }
        log.error("未知的出版社：{}", value);
        return "北师大版";
    }

}
