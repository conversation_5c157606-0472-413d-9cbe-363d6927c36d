package com.joinus.edu.analytics.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Description: 登录接口参数
 * @Author:  anpy
 * @date:  2025/4/16 15:46
 */
@Data
public class AccountLoginParam {

    @Schema(description = "账号")
    @NotBlank(message = "账号不能为空")
    private String account;
    @Schema(description = "密码")
    @NotBlank(message = "密码不能为空")
    private String password;

}
