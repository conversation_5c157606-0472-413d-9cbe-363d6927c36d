package com.joinus.edu.analytics.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.edu.analytics.model.enums.QuestionTypeEnum;
import com.joinus.edu.analytics.model.enums.ResultEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * u4e2au4ebau8bd5u5377u505au9898u7ed3u679cu5b9eu4f53u7c7b
 */
@Data
@TableName("personal_exam_question")
public class PersonalExamQuestion {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    private Long personalExamId;
    
    private UUID questionId;
    
    private QuestionTypeEnum questionType;
    
    private ResultEnum result;
    
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;

    private LocalDateTime deletedAt;

    private Integer sortNo;
}
