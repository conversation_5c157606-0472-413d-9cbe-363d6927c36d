package com.joinus.edu.analytics.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StudentAnalyticsDto {

    private String examName;
    private String className;
    private String studentName;
    private String reportTime;
    private BigDecimal classCorrectRate;
    private String gradeCorrectRate;
    private Integer errorQuestionCount;
    private Integer allQuestionCount;
    private Integer studentCount;
    private Double percentile;
    private UUID examId;
    private Integer classRanking;
    private BigDecimal personalCorrectRate;
    private String studentImg;
    private Integer examClassStudentCount;
}
