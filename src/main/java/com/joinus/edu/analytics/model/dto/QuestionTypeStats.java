package com.joinus.edu.analytics.model.dto;

import com.joinus.edu.analytics.model.enums.QuestionTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 题型统计数据
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QuestionTypeStats {
    
    /**
     * 题目类型
     */
    private QuestionTypeEnum questionType;
    
    /**
     * 题目类型描述
     */
    private String questionTypeDesc;
    
    /**
     * 题目总数
     */
    private Integer totalCount;
    
    /**
     * 正确题目数量
     */
    private Integer correctCount;
    
    /**
     * 错误题目数量
     */
    private Integer mistakeCount;
    
    /**
     * 正确率
     */
    private String correctRate;
}
