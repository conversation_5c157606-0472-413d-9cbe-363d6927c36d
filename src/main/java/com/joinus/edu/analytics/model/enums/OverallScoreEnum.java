package com.joinus.edu.analytics.model.enums;

/**
 * u8003u8bd5u6210u7ee9u7b49u7ea7u679au4e3e
 */
public enum OverallScoreEnum {
    
    EXCELLENT("u4f18"),
    GOOD("u826f"),
    FAIR("u5dee");
    
    private final String description;
    
    OverallScoreEnum(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
}
