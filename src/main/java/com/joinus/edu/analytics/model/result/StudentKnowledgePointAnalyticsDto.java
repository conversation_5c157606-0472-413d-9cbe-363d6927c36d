package com.joinus.edu.analytics.model.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;
import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StudentKnowledgePointAnalyticsDto {

    private UUID questionId;
    private UUID knowledgePointId;
    private String knowledgePointName;
    private BigDecimal classCorrectRate;
    private BigDecimal gradeCorrectRate;
    private Long personalExamId;
    private Set<Integer> questionSortNoList;
    private BigDecimal masteryLevel;
}
