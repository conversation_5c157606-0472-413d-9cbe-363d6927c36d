package com.joinus.edu.analytics.model.key;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Description: 年级键对象，用于分组
 * @Author: anpy
 * @date: 2025/4/16 16:05
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class GradeKey {
    /**
     * 年级ID
     */
    private Long gradeId;
    
    /**
     * 年级名称
     */
    private String gradeName;
}
