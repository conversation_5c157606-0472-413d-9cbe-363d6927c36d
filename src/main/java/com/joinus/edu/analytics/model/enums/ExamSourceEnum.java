package com.joinus.edu.analytics.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

import java.util.List;

@Getter
public enum ExamSourceEnum {
    USER_UPLOAD("用户上传"),
    SPECIAL_TRAINING("专项训练"),
    PAST_EXAM_PAPER("中考真题"),
    EXAM_BLIND_SPOTS_TRAINING("考点盲区训练"),
    HOLIDAY_TRAINING("暑期训练"),
    REGULAR_EXAM_PAPER("常规考试卷"),
    SYNC_LEARNING_TEST("同步学练测"),
    KNOWLEDGE_POINTS_TRAINING("知识点专项训练"),
    MISTAKE_SET_TRAINING("错题集专项训练");

    @EnumValue
    private final String name;


    ExamSourceEnum(String name) {
        this.name = name;
    }


    public static List<ExamSourceEnum> listTraining() {
        return List.of(SPECIAL_TRAINING, EXAM_BLIND_SPOTS_TRAINING, HOLIDAY_TRAINING, SYNC_LEARNING_TEST);
    }
    public static List<ExamSourceEnum> listGradeTraining() {
        return List.of(SPECIAL_TRAINING, EXAM_BLIND_SPOTS_TRAINING, HOLIDAY_TRAINING, SYNC_LEARNING_TEST,REGULAR_EXAM_PAPER,PAST_EXAM_PAPER);
    }

    public static List<ExamSourceEnum> listRealExam() {
        return List.of(PAST_EXAM_PAPER, REGULAR_EXAM_PAPER);
    }
}
