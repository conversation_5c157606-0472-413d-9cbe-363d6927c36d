package com.joinus.edu.analytics.model.param;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Description 分页查询参数
 * @date 2023/6/5 16:48
 **/
@Data
@ToString
public class PageParam {

    private Integer size = 100;
    private Integer current = 1;

    public <K> Page<K> page(){
        return new Page<>(current, size);
    }
}
