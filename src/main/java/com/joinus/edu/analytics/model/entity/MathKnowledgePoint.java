package com.joinus.edu.analytics.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.UUID;

/**
 * 
 * @TableName math_knowledge_points
 */
@Data
@TableName(value ="math_knowledge_points")
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class MathKnowledgePoint implements Serializable {

    private UUID id;
    /**
     * 
     */
    private String name;

    /**
     * 知识点描述
     */
    private String content;

    /**
     * 知识点排序
     */
    private Integer sortNo;

    private Boolean examPoint;

    private Boolean isBase;

    private Date createdAt;

    private Date updatedAt;

    private Date deletedAt;
}
