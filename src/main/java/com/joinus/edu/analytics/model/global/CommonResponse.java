package com.joinus.edu.analytics.model.global;


import com.joinus.edu.analytics.exception.BaseException;
import com.joinus.edu.analytics.exception.CommonExceptionAssert;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>通用返回结果</p>
 *
 * <AUTHOR>
 * @date 2019/5/2
 */
@Getter
@AllArgsConstructor
public enum CommonResponse implements CommonExceptionAssert {

    // 系统模块
    SUCCESS(200, "操作成功"),
    ERROR(300, "操作失败"),
    LOGIN_ERROR(401, "未登录或登录已过期！"),
    LOGIN_VERIFY(402, "需要验证！"),
    TERMINAL_NO_EXIST(501, "查询不到设备信息！"),
    STUDENT_NO_EXIST(502,"查询不到学生信息"),
    STUDENT_NOT_IN_SCHOOL(503, "学生不属于该学校！"),
    IDENTITY_IS_NULL(504,"学生身份证号为空"),
    NOT_SUFFICIENT_FUNDS(505,"账户余额不足"),
    ACCOUNT_EXCEPTION(506,"账户异常"),
    //custom exception
    ALREADY_EXIST(3001, "数据已存在"),
    ALREADY_EXIST_REFRESH(3002, "数据已存在,刷新"),
    ALREADY_EXIST_SMS(3003, "数据已存在,需要短信验证"),
    REQUEST_UNSUCCESSFUL(4001, "请求处理失败"),
    BUSINESS_EXPIRED(4002, "业务未开通或已过期"),
    PERMISSIONS_DENIED(4003, "token校验失败"),
    NOT_FOUND(4004, "未查询到相关信息"),
    UN_SUPPORT_METHOD(4005, "请求方法错误,请核验后再试"),
    SYSTEM_EXCEPTION(5000, "系统处理错误"),
    CODE_INVALID(5001,"验证码不正确"),
    PARAMETER_VALID_ERROR(6001, "参数校验异常"),
    SERVER_BUSY(9999, "服务器繁忙"),

    ADD_PARENT_THREE_COUNT(301, "该学生已绑定三个家长,无法继续添加"),

    ADD_PARENT_TEN_COUNT(301, "该学生已绑定十个家长,无法继续添加"),
    ;

    /**
     * 返回码
     */
    private int code;
    /**
     * 返回消息
     */
    private String msg;

    public static void assertError(String msg){
        throw new BaseException(ERROR.code, msg);
    }

    public static void assertError(CommonResponse response){
        throw new BaseException(response.getCode(), response.getMsg());
    }

}
