package com.joinus.edu.analytics.model.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.edu.analytics.model.enums.ExamSourceEnum;
import com.joinus.edu.analytics.model.enums.PublisherEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 试卷实体类
 */
@Data
@TableName("math_exams")
public class MathExam {
    
    @TableId
    private UUID id;
    
    private String name;
    
    private Integer semester;
    
    private Integer grade;

    private ExamSourceEnum source;

    private PublisherEnum publisher;
    
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;

    private LocalDateTime deletedAt;
}
