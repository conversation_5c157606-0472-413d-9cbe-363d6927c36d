package com.joinus.edu.analytics.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 试卷实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("math_exams")
public class MathExams {
    
    /**
     * 试卷ID
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private UUID id;
    
    /**
     * 试卷名称
     */
    private String name;
    
    /**
     * 学期 1上学期 2下学期
     */
    private Integer semester;
    
    /**
     * 年级 7 8 9
     */
    private Integer grade;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 删除时间
     */
    private LocalDateTime deletedAt;
}
