package com.joinus.edu.analytics.model.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.UUID;

@Data
@Schema(description = "数学知识点")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MathKnowledgePointResult implements Serializable {

    @Schema(description = "知识点id", implementation = UUID.class, example = "d0a0d0a0-d0a0-d0")
    private UUID id;

    @Schema(description = "知识点名称", implementation = String.class, example = "1")
    private String name;

    @Schema(description = "排序序号", implementation = Integer.class, example = "1")
    private Integer sortNo;

}
