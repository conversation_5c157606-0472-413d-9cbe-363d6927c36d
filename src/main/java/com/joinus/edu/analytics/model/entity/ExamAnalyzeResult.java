package com.joinus.edu.analytics.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.edu.analytics.model.enums.ExamAnalyzeResultEnum;
import com.joinus.edu.analytics.model.enums.OverallScoreEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 考情分析结果实体类
 */
@Data
@TableName("exam_analyze_result")
public class ExamAnalyzeResult {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    private UUID examId;
    
    private Long studentId;
    
    private ExamAnalyzeResultEnum result;
    
    private OverallScoreEnum overallScore;
    
    private String correctRate; // JSONB类型，使用String表示
    
    private BigDecimal percentile;
    
    private Integer totalKnowledgePoints;
    
    private Integer masteredKnowledgePoints;
    
    private Integer weakKnowledgePoints;
    
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;
    private LocalDateTime deletedAt;
    
    private Long personalExamId;
    
    private Long parentId;
}
