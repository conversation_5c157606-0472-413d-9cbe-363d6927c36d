package com.joinus.edu.analytics.model.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 获取学生最近考情分析记录参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class RecentExamAnalyticsParam extends PageParam {
    /**
     * 学生ID
     */
    private Long studentId;
}
