package com.joinus.edu.analytics.model.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExamInfoResult {

    private UUID examId;
    private String examName;
    private List<Data> examList;

    @lombok.Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Data{
        private String questionType;
        private String questionId;
        private String questionContent;
        private Integer sortNo;
        private String difficultyStr;
        private String answer;
        private String analysis;
        private List<String> ossUrl;
        private String knowledgePointNames;
    }

}
