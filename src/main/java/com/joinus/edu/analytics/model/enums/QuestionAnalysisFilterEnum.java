package com.joinus.edu.analytics.model.enums;

/**
 * 题目分析过滤类型枚举
 */
public enum QuestionAnalysisFilterEnum {

    /**
     * 所有题目
     */
    ALL("所有题目"),
    
    /**
     * 错题
     */
    WRONG_ONLY("错题");

    private final String desc;
    
    QuestionAnalysisFilterEnum(String desc) {
        this.desc = desc;
    }

    /**
     * 根据描述获取枚举
     *
     * @param desc 描述文本
     * @return 对应的枚举值，如果没有匹配则返回ALL
     */
    public static QuestionAnalysisFilterEnum getEnumByDesc(String desc) {
        for (QuestionAnalysisFilterEnum filterEnum : QuestionAnalysisFilterEnum.values()) {
            if (filterEnum.getDesc().equals(desc)) {
                return filterEnum;
            }
        }
        return QuestionAnalysisFilterEnum.ALL;
    }

    /**
     * 获取所有过滤类型枚举值
     *
     * @return 所有过滤类型的枚举数组
     */
    public static QuestionAnalysisFilterEnum[] getAllTypes() {
        return QuestionAnalysisFilterEnum.values();
    }

    public String getDesc() {
        return desc;
    }
}
