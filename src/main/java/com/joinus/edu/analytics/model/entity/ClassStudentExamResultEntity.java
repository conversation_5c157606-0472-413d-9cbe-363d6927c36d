package com.joinus.edu.analytics.model.entity;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 班级学生考试结果实体类
 * 对应getClassStudentsWithExamResults查询结果
 */
@Data
public class ClassStudentExamResultEntity {

    /**
     * 学生ID
     */
    private Long studentId;
    
    /**
     * 学生姓名
     */
    private String studentName;
    
    /**
     * 学号
     */
    private Integer studentNo;
    private Long classId;
    
    /**
     * 班级名称
     */
    private String className;
    
    /**
     * 年级名称
     */
    private String gradeName;
    private Long gradeId;
    
    /**
     * 考试结果状态
     */
    private String result;

    /**
     * 正确率百分位数
     */
    private String correctRate;

    /**
     * 薄弱知识点数量
     */
    private Integer weakKnowledgePoints;
    
    /**
     * 报告生成时间
     */
    private String reportTime;
    
    /**
     * 错题数量
     */
    private Integer errorQuestionCount;
    private Long analyzeId;
    private Long personalExamId;
}
