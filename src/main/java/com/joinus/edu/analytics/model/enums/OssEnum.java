package com.joinus.edu.analytics.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum OssEnum {

    MINIO_EDU_KNOWLEDGE_HUB("minio", "edu-knowledge-hub"),
    ALIYUN_EDU_KNOWLEDGE_HUB("aliyun", "edu-knowledge-hub");

    private final String type;

    private final String bucket;


    public static OssEnum ofTypeAndBucket(String type, String bucket) {
        // 找到第一个匹配项即可停止遍历
        return Arrays.stream(OssEnum.values())
                .filter(e -> e.getType().equals(type) && e.getBucket().equals(bucket))
                .findFirst()
                .orElse(MINIO_EDU_KNOWLEDGE_HUB);
    }

}
