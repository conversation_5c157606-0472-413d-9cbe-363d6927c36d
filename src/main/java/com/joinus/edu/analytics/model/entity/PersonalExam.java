package com.joinus.edu.analytics.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.edu.analytics.model.enums.BookVolumeEnum;
import com.joinus.edu.analytics.model.enums.PublisherEnum;
import lombok.*;

import java.util.UUID;

/**
 *
 * @TableName personal_exam
 */
@TableName(value ="personal_exam")
@Data
public class PersonalExam extends BaseEntity {
    /**
     * 学生ID
     */
    private Long studentId;

    /**
     * 试卷ID
     */
    private UUID examId;

    /**
     * examSource
     */
    private String flag;

    private PublisherEnum publisher;

    /**
     * 试卷名称
     */
    private String examName;

    private Integer grade;

    private Integer semester;

    private BookVolumeEnum bookVolume;

}
