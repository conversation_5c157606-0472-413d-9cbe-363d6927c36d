package com.joinus.edu.analytics.model.po;

import com.joinus.edu.analytics.model.enums.PublisherEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.UUID;

@Data
public class MathPersonalExamKnowledgePointPo implements Serializable {

    private Long personalExamId;
    private PublisherEnum examPublisher;
    private Long examAnalyzeResultId;
    private UUID examId;
    private UUID knowledgePointId;
    private PublisherEnum knowledgePointPublisher;
}
