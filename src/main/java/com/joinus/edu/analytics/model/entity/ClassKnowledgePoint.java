package com.joinus.edu.analytics.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * u73edu7ea7u77e5u8bc6u70b9u5206u6790u5b9eu4f53u7c7b
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("class_knowledge_point")
public class ClassKnowledgePoint {
    
    /**
     * u4e3bu952eID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * u73edu7ea7ID
     */
    private Long classId;
    
    /**
     * u5e74u7ea7ID
     */
    private Long gradeId;
    
    /**
     * u8bd5u5377ID
     */
    private UUID examId;
    
    /**
     * u9898u76eeID
     */
    private UUID questionId;
    
    /**
     * u77e5u8bc6u70b9ID
     */
    private UUID knowledgePointId;
    
    /**
     * u9519u8befu7387
     */
    private BigDecimal errorRate;
    
    /**
     * u9519u8befu4ebau6570
     */
    private Integer errorCount;
    
    /**
     * u521bu5efau65f6u95f4
     */
    private LocalDateTime createdAt;
    
    /**
     * u66f4u65b0u65f6u95f4
     */
    private LocalDateTime updatedAt;
    
    /**
     * u903bu8f91u5220u9664
     */
    private LocalDateTime deletedAt;
}
