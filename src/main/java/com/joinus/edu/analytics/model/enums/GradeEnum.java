package com.joinus.edu.analytics.model.enums;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Getter
@AllArgsConstructor
public enum GradeEnum {
    PRIMARY_SCHOOL_3(3, "三年级", SchoolStageEnum.PRIMARY, CollUtil.toList(BookVolumeEnum.UPPER, BookVolumeEnum.LOWER)),
    PRIMARY_SCHOOL_4(4, "四年级", SchoolStageEnum.PRIMARY, CollUtil.toList(BookVolumeEnum.UPPER, BookVolumeEnum.LOWER)),
    PRIMARY_SCHOOL_5(5, "五年级", SchoolStageEnum.PRIMARY, CollUtil.toList(BookVolumeEnum.UPPER, BookVolumeEnum.LOWER)),
    PRIMARY_SCHOOL_6(6, "六年级", SchoolStageEnum.PRIMARY, CollUtil.toList(BookVolumeEnum.UPPER, BookVolumeEnum.LOWER)),
    MIDDLE_SCHOOL_1(7, "七年级", SchoolStageEnum.JUNIOR, CollUtil.toList(BookVolumeEnum.UPPER, BookVolumeEnum.LOWER)),
    MIDDLE_SCHOOL_2(8, "八年级", SchoolStageEnum.JUNIOR, CollUtil.toList(BookVolumeEnum.UPPER, BookVolumeEnum.LOWER)),
    MIDDLE_SCHOOL_3(9, "九年级", SchoolStageEnum.JUNIOR, CollUtil.toList(BookVolumeEnum.UPPER, BookVolumeEnum.LOWER)),
    HIGH_SCHOOL_1(10, "高一", SchoolStageEnum.SENIOR, CollUtil.toList(BookVolumeEnum.UPPER, BookVolumeEnum.LOWER)),
    HIGH_SCHOOL_2(11, "高二", SchoolStageEnum.SENIOR, CollUtil.toList(BookVolumeEnum.UPPER, BookVolumeEnum.LOWER)),
    HIGH_SCHOOL_3(12, "高三", SchoolStageEnum.SENIOR, CollUtil.toList(BookVolumeEnum.UPPER, BookVolumeEnum.MIDDLE, BookVolumeEnum.LOWER));

    @EnumValue
    private final Integer value;

    private final String desc;

    private final SchoolStageEnum schoolStage;

    private final List<BookVolumeEnum> bookVolumes;

    public static GradeEnum ofValue(Integer currentGradeLevel) {
        Optional<GradeEnum> first = Arrays.stream(GradeEnum.values()).filter(gradeEnum -> gradeEnum.value.equals(currentGradeLevel)).findFirst();
        return first.orElse(null);
    }
}
