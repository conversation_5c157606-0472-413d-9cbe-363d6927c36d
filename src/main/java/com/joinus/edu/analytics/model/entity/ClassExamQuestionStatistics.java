package com.joinus.edu.analytics.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 班级考试题目统计实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("class_exam_question_statistics")
public class ClassExamQuestionStatistics {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 班级ID
     */
    private Long classId;
    
    /**
     * 年级ID
     */
    private Long gradeId;
    
    /**
     * 试卷ID
     */
    private UUID examId;
    
    /**
     * 题目ID
     */
    private UUID questionId;
    
    /**
     * 正确率
     */
    private BigDecimal correctRate;
    
    /**
     * 正确人数
     */
    private Integer correctCount;
    
    /**
     * 班级考试ID
     */
    private Long classExamId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 删除时间
     */
    private LocalDateTime deletedAt;
    private Integer sortNo;
}
