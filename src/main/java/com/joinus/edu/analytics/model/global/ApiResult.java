package com.joinus.edu.analytics.model.global;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 响应对象模型定义
 *
 * <AUTHOR> anpy
 * @create 2023/6/30 15:46
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "响应返回数据对象")
public class ApiResult<T> implements Serializable {

    @Schema(description = "响应码")
    private Integer code;

    private String message;

    private T data;

    public static <T> ApiResult<T> result(Integer code) {
        return ApiResult.<T>builder().code(code).build();
    }

    public static <T> ApiResult<T> result(Integer code, String message) {
        return ApiResult.<T>builder().code(code).message(message).build();
    }

    public static <T> ApiResult<T> result(Integer code, String message, T data) {
        return ApiResult.<T>builder().code(code).message(message).data(data).build();
    }

    //------------------------------------------------------------------------------


    public static <T> ApiResult<T> success() {
        return ApiResult.<T>builder().code(ApiResultCode.SUCCESS.getCode()).message(ApiResultCode.SUCCESS.getMessage()).build();
    }

    public static <T> ApiResult<T> success(T data) {
        return ApiResult.<T>builder().code(ApiResultCode.SUCCESS.getCode()).message(ApiResultCode.SUCCESS.getMessage()).data(data).build();
    }

    public static <T> ApiResult<T> failed(String message) {
        return ApiResult.<T>builder().code(ApiResultCode.FAIL.getCode()).message(message).build();
    }

    public static <T> ApiResult<T> failed(Integer code, String message) {
        return ApiResult.<T>builder().code(code).message(message).build();
    }
}