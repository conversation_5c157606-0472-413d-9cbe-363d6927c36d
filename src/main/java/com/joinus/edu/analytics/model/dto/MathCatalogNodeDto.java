package com.joinus.edu.analytics.model.dto;

import com.joinus.edu.analytics.model.enums.PublisherEnum;
import com.joinus.edu.analytics.model.po.MathKnowledgePointPo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MathCatalogNodeDto implements Serializable {

    @Schema(description = "节点id")
    private UUID id;
    @Schema(description = "父节点id")
    private UUID parentId;
    @Schema(description = "节点名称")
    private String name;
    @Schema(description = "节点名称前缀")
    private String  namePrefix;
    @Schema(description = "节点层级")
    private Integer level;

    private Integer sortNo;

    private Boolean isLeaf;

    private UUID bookId;

    private Integer grade;

    private PublisherEnum publisher;

    private Integer semester;

    private List<MathCatalogNodeDto> subNodes;

    private List<MathKnowledgePointPo> knowledgePoints;
}
