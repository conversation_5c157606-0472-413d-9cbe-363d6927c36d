package com.joinus.edu.analytics.model.dto;

import com.joinus.edu.analytics.model.enums.ExamAnalyzeResultEnum;
import com.joinus.edu.analytics.model.enums.ExamSourceEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * @Description 个人考试
 * <AUTHOR>
 * @Date 2025/9/18 10:56
 **/
@Data
public class PersonalExamDto implements Serializable {
    @Schema(description = "考试ID")
    private Long personalExamId;
    @Schema(description = "学生ID")
    private Long studentId;
    @Schema(description = "试卷ID")
    private UUID examId;
    @Schema(description = "试卷名称")
    private String examName;
    @Schema(description = "试卷来源")
    private ExamSourceEnum source;
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
    @Schema(description = "分析状态")
    private ExamAnalyzeResultEnum analyzeResult;
    @Schema(description = "分析ID")
    private Long examAnalyzeResultId;
}
