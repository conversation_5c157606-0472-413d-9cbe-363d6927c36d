
package com.joinus.edu.analytics.model.result;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class ClassInfoDataDTO {

    private int code;
    private String msg;
    private DataDTO data;

    @NoArgsConstructor
    @Data
    public static class DataDTO {
        private int studentNum;
        private List<ListDTO> list;

        @NoArgsConstructor
        @Data
        public static class ListDTO {
            private Long classId;
            private String className;
            private Long gradeId;
            private String gradeName;
        }
    }
}
