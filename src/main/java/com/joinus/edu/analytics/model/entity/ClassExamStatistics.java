package com.joinus.edu.analytics.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.UUID;

/**
 * @Description: 班级考试统计表
 * @Author: anpy
 * @date: 2025/4/9
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("class_exam_statistics")
public class ClassExamStatistics {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 班级ID
     */
    private Long classId;
    private Long gradeId;
    
    /**
     * 试卷ID
     */
    private UUID examId;
    
    /**
     * 考试场次ID
     * 用于区分同一班级同一试卷的不同考试场次
     */
    @TableField(exist = false)
    private String examSession;
    
    /**
     * 班级平均正确率
     */
    private BigDecimal correctRate;
    
    /**
     * 参与考试人数
     */
    private Integer studentCount;
    
    /**
     * 正确答案数量
     */
    @TableField(exist = false)
    private Integer correctCount;
    
    /**
     * 创建时间
     */
    private Date createdAt;
    
    /**
     * 更新时间
     */
    private Date updatedAt;
    
    /**
     * 删除时间
     */
    private Date deletedAt;
}
