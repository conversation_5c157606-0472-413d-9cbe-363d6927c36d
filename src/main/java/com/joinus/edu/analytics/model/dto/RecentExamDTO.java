package com.joinus.edu.analytics.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 最近考试信息DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "最近考试信息DTO")
public class RecentExamDTO {
    
    @Schema(description = "试卷ID", example = "a1b2c3d4-e5f6-7890-abcd-ef1234567890")
    private UUID examId;
    
    @Schema(description = "试卷名称", example = "2025年春季期中考试")
    private String examName;
    
    @Schema(description = "考试日期", example = "2025-03-15T09:00:00")
    private LocalDateTime examDate;
    
    @Schema(description = "班级数据列表")
    private List<ClassExamDataDTO> classDataList;
}
