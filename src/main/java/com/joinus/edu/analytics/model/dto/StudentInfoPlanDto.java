package com.joinus.edu.analytics.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description 学生学习计划
 * <AUTHOR>
 * @Date 2025/9/17 17:24
 **/
@Data
public class StudentInfoPlanDto implements Serializable {
    private static final long serialVersionUID = 1L;
    @Schema(description = "学生ID")
    private Long studentId;
    @Schema(description = "班级ID")
    private Integer grade;
    @Schema(description = "班级名称")
    private String gradeName;
    @Schema(description = "学生name")
    private String studentName;
    @Schema(description = "手机号码")
    private String parentTelNum;
    @Schema(description = "学习计划ID")
    private String parentName;
    @Schema(description = "是否开启学习计划")
    private Boolean studyPlanEnabled;
}
