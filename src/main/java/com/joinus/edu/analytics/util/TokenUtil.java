package com.joinus.edu.analytics.util;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.security.Key;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Token工具类
 */
@Slf4j
@Component
public class TokenUtil {

    // 默认密钥（如果配置文件中未指定）
    private static final Key DEFAULT_KEY = Keys.secretKeyFor(SignatureAlgorithm.HS256);
    
    // 从配置文件中读取密钥，如果未配置则使用默认密钥
    @Value("${jwt.secret:}")
    private String secret;
    
    // token有效期（毫秒）
    @Value("${jwt.expiration:86400000}")
    private long expiration;
    
    /**
     * 生成token
     * @param userId 用户ID
     * @return token字符串
     */
    public String generateToken(Long userId) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userId);
        return createToken(claims);
    }
    
    /**
     * 从token中获取用户ID
     * @param token token字符串
     * @return 用户ID
     */
    public Long getUserIdFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims != null ? Long.valueOf(claims.get("userId").toString()) : null;
    }
    
    /**
     * 验证token是否有效
     * @param token token字符串
     * @return 是否有效
     */
    public boolean validateToken(String token) {
        try {
            Claims claims = getClaimsFromToken(token);
            return claims != null && !isTokenExpired(claims);
        } catch (Exception e) {
            log.error("Token验证失败", e);
            return false;
        }
    }
    
    /**
     * 从token中获取Claims
     * @param token token字符串
     * @return Claims对象
     */
    private Claims getClaimsFromToken(String token) {
        try {
            return Jwts.parserBuilder()
                    .setSigningKey(getSigningKey())
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
        } catch (Exception e) {
            log.error("获取Claims失败", e);
            return null;
        }
    }
    
    /**
     * 判断token是否过期
     * @param claims Claims对象
     * @return 是否过期
     */
    private boolean isTokenExpired(Claims claims) {
        Date expiration = claims.getExpiration();
        return expiration != null && expiration.before(new Date());
    }
    
    /**
     * 创建token
     * @param claims 存储的数据
     * @return token字符串
     */
    private String createToken(Map<String, Object> claims) {
        Date now = new Date();
        Date expirationDate = new Date(now.getTime() + expiration);
        
        return Jwts.builder()
                .setClaims(claims)
                .setIssuedAt(now)
                .setExpiration(expirationDate)
                .signWith(getSigningKey())
                .compact();
    }
    
    /**
     * 获取签名密钥
     * @return 密钥对象
     */
    private Key getSigningKey() {
        if (secret == null || secret.isEmpty()) {
            return DEFAULT_KEY;
        }
        return Keys.hmacShaKeyFor(secret.getBytes());
    }
}
