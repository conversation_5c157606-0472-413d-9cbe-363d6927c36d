package com.joinus.edu.analytics.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.joinus.edu.analytics.model.global.Constants;
import com.joinus.edu.analytics.model.result.AccountLoginResult;
import com.joinus.edu.analytics.utils.JWTUtil;
import com.joinus.edu.analytics.utils.RedisUtils;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class UserUtil {

    private static final Integer TOKEN_EXPIRE_SECONDS  = 60 * 60 * 8;

    public static AccountLoginResult.DataDTO getCurrentUserByToken(String token) {
        Claims claims = JWTUtil.parseJWT(token);
        AccountLoginResult.DataDTO user = JSONUtil.toBean(StrUtil.toString(claims.get("teacher")), AccountLoginResult.DataDTO.class);
        if(user != null){
            refreshToken(user);
        }

        return user;
    }

    public static void refreshToken(AccountLoginResult.DataDTO user){
        RedisUtils.expire(Constants.LOGIN_TOKEN_KEY + user.getId(), TOKEN_EXPIRE_SECONDS);
    }

}
