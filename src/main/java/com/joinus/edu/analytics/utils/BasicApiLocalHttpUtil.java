package com.joinus.edu.analytics.utils;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

@Slf4j
public class BasicApiLocalHttpUtil {

    private static final String INFO_SOURCE = "SMART_STUDY";

    public static String get(String url, String operator,String telNum) throws Exception {
        try {
            String encodedOperator = URLEncoder.encode(operator, StandardCharsets.UTF_8);
            HttpResponse response = HttpRequest.get(url)
                    .header("operator", encodedOperator)
                    .header("infoSource", INFO_SOURCE)
                    .header("telNum", telNum)
                    .timeout(30000)
                    .execute();
            if (200 == response.getStatus()) {
                return response.body();
            }
            log.warn("远程调用basic api失败:" + url + response.body());
            return null;
        } catch (Exception e) {
            log.warn("远程调用basic api失败:" + url + e.getMessage());
            throw new Exception("远程调用basic api" + url + "失败");
        }
    }

    public static String post(String url, Object body, String  operator, String operateType) throws Exception {
        try {
            String encodedOperator = URLEncoder.encode(operator, StandardCharsets.UTF_8);
            HttpResponse response = HttpRequest.post(url)
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .header("operator", encodedOperator)
                    .header("infoSource", INFO_SOURCE)
                    .header("operateType", operateType)
                    .body(JSONUtil.toJsonStr(body))
                    .timeout(30000)
                    .execute();
            if (200 == response.getStatus()) {
                return response.body();
            }
            log.warn("远程调用basic api失败:" + url + response.body());
            return null;
        } catch (Exception e) {
            log.error("远程调用basic api" + url + "应用失败:" + e.getMessage());
            throw new Exception("远程调用basic api");
        }
    }



}
