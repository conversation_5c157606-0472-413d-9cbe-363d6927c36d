package com.joinus.edu.analytics.utils;

import cn.hutool.json.JSONUtil;
import com.joinus.edu.analytics.exception.BaseException;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Map;

/**
 * 基于OkHttp的HTTP工具类，使用连接池优化性能
 *
 * <AUTHOR>
 * @date 2025/4/28
 */
@Slf4j
@Component
public class OkHttpUtil {

    private final OkHttpClient okHttpClient;

    public OkHttpUtil(OkHttpClient okHttpClient) {
        this.okHttpClient = okHttpClient;
    }

    /**
     * 执行GET请求
     *
     * @param url    请求URL
     * @param params 请求参数
     * @return 响应内容
     */
    public String get(String url, Map<String, Object> params) {
        HttpUrl.Builder urlBuilder = HttpUrl.parse(url).newBuilder();
        
        // 添加查询参数
        if (params != null && !params.isEmpty()) {
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                if (entry.getValue() != null) {
                    urlBuilder.addQueryParameter(entry.getKey(), entry.getValue().toString());
                }
            }
        }

        Request request = new Request.Builder()
                .url(urlBuilder.build())
                .get()
                .build();

        return execute(request, url);
    }

    /**
     * 执行POST请求（JSON格式）
     *
     * @param url  请求URL
     * @param body 请求体
     * @return 响应内容
     */
    public String postJson(String url, Object body) {
        MediaType JSON = MediaType.parse("application/json; charset=utf-8");
        String jsonBody = body != null ? JSONUtil.toJsonStr(body) : "";
        
        RequestBody requestBody = RequestBody.create(jsonBody, JSON);
        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .build();

        return execute(request, url, jsonBody);
    }

    /**
     * 执行POST请求（表单格式）
     *
     * @param url    请求URL
     * @param params 表单参数
     * @return 响应内容
     */
    public String postForm(String url, Map<String, Object> params) {
        FormBody.Builder formBuilder = new FormBody.Builder();
        
        // 添加表单参数
        if (params != null && !params.isEmpty()) {
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                if (entry.getValue() != null) {
                    formBuilder.add(entry.getKey(), entry.getValue().toString());
                }
            }
        }

        Request request = new Request.Builder()
                .url(url)
                .post(formBuilder.build())
                .build();

        return execute(request, url);
    }

    /**
     * 执行PUT请求（JSON格式）
     *
     * @param url  请求URL
     * @param body 请求体
     * @return 响应内容
     */
    public String putJson(String url, Object body) {
        MediaType JSON = MediaType.parse("application/json; charset=utf-8");
        String jsonBody = body != null ? JSONUtil.toJsonStr(body) : "";
        
        RequestBody requestBody = RequestBody.create(jsonBody, JSON);
        Request request = new Request.Builder()
                .url(url)
                .put(requestBody)
                .build();

        return execute(request, url, jsonBody);
    }

    /**
     * 执行HTTP请求并处理响应
     *
     * @param request HTTP请求对象
     * @param url     请求URL（用于日志）
     * @return 响应内容
     */
    private String execute(Request request, String url) {
        return execute(request, url, null);
    }

    /**
     * 执行HTTP请求并处理响应
     *
     * @param request HTTP请求对象
     * @param url     请求URL（用于日志）
     * @param body    请求体（用于日志）
     * @return 响应内容
     */
    private String execute(Request request, String url, String body) {
        long startTime = System.currentTimeMillis();
        try {
            Response response = okHttpClient.newCall(request).execute();
            long duration = System.currentTimeMillis() - startTime;
            
            if (response.isSuccessful()) {
                String responseBody = response.body() != null ? response.body().string() : "";
                log.info("HTTP请求成功: URL={}, 耗时={}ms", url, duration);
                return responseBody;
            } else {
                log.error("HTTP请求失败: URL={}, 状态码={}, 耗时={}ms", url, response.code(), duration);
                throw new BaseException("获取服务失败，状态码: " + response.code());
            }
        } catch (IOException e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("HTTP请求异常: URL={}, 异常={}, 耗时={}ms", url, e.getMessage(), duration);
            throw new BaseException("获取服务失败: " + e.getMessage());
        }
    }
}
