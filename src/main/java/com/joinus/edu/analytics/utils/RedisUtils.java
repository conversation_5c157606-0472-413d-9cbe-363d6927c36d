package com.joinus.edu.analytics.utils;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * Redis工具类
 *
 * <AUTHOR>
 */
@Component
public class RedisUtils {

    private static RedisTemplate<String, Object> redisTemplate;

    public RedisUtils(RedisTemplate<String, Object> redisTemplate) {
        RedisUtils.redisTemplate = redisTemplate;
    }

    /**
     * 设置Redis缓存
     *
     * @param key   缓存键
     * @param value 缓存值
     * @return 是否成功
     */
    public static boolean set(String key, Object value) {
        try {
            redisTemplate.opsForValue().set(key, value);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 设置Redis缓存并设置过期时间
     *
     * @param key     缓存键
     * @param value   缓存值
     * @param timeout 过期时间（秒）
     * @return 是否成功
     */
    public static boolean set(String key, Object value, long timeout) {
        try {
            if (timeout > 0) {
                redisTemplate.opsForValue().set(key, value, timeout, TimeUnit.SECONDS);
            } else {
                set(key, value);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取Redis缓存
     *
     * @param key 缓存键
     * @return 缓存值
     */
    public static String get(String key) {
        return key == null ? null : (String) redisTemplate.opsForValue().get(key);
    }

    /**
     * 获取指定类型的Redis缓存
     *
     * @param key   缓存键
     * @param clazz 返回类型
     * @param <T>   指定类型
     * @return 缓存值
     */
    public static <T> T get(String key, Class<T> clazz) {
        Object obj = get(key);
        return obj == null ? null : clazz.cast(obj);
    }

    /**
     * 删除Redis缓存
     *
     * @param key 缓存键
     * @return 是否成功
     */
    public static boolean delete(String key) {
        return Boolean.TRUE.equals(redisTemplate.delete(key));
    }

    /**
     * 检查键是否存在
     *
     * @param key 缓存键
     * @return 是否存在
     */
    public static boolean hasKey(String key) {
        return Boolean.TRUE.equals(redisTemplate.hasKey(key));
    }

    /**
     * 设置过期时间
     *
     * @param key     缓存键
     * @param timeout 过期时间（秒）
     * @return 是否成功
     */
    public static boolean expire(String key, long timeout) {
        try {
            if (timeout > 0) {
                redisTemplate.expire(key, timeout, TimeUnit.SECONDS);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}
