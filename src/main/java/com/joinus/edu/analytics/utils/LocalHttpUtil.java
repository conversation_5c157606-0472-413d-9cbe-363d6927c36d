package com.joinus.edu.analytics.utils;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.google.gson.Gson;
import com.joinus.edu.analytics.exception.BaseException;
import com.joinus.edu.analytics.model.global.CommonResponse;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

@Slf4j
public class LocalHttpUtil {

    /**
     * 通用的 HTTP GET 请求方法
     *
     * @param url    请求的 URL
     * @param params 请求参数，以键值对的形式存储在 Map 中
     * @return HttpResponse body
     */
    public static String get(String url, Map<String, Object> params) {
        HttpRequest request = HttpUtil.createGet(url);
        return execute(params, request, url);
    }

    /**
     * 通用的 HTTP POST 请求方法（JSON数据）
     *
     * @param url    请求的 URL
     * @param params 请求参数，以键值对的形式存储在 Map 中
     * @return HttpResponse body
     */
    public static String post(String url, Object params) {
        HttpRequest request = HttpUtil.createPost(url);
        request.contentType("application/json");
        if (params != null) {
            request.body(JSONUtil.toJsonStr(params));
        }
        return executeJson(request, url, JSONUtil.toJsonStr(params));
    }

    private static String executeXForm(HttpRequest request, String url) {
        request.timeout(20000);
        HttpResponse response = request.execute();
        if (response.isOk()) {
            return response.body();
        }
        log.info("{} Request failed, status code: {}", url, response.getStatus());
        throw new BaseException("获取AI服务失败");
    }

    private static String executeJson(HttpRequest request, String url, String param) {
        request.timeout(30000);
        HttpResponse response = request.execute();
        try {
            // 处理response对象，确保日志内容不包含换行符
            String responseStr = response.toString().replaceAll("[\r\n]+", " ");
            log.info("AI请求返回结果，url：{} ,参数:{} ,结果 :{}", url, param.replaceAll("[\r\n]+", " "), responseStr);
        } catch (Exception e) {
            // 处理异常信息，确保日志内容不包含换行符
            String errorMsg = e.getMessage();
            if (errorMsg != null) {
                errorMsg = errorMsg.replaceAll("[\r\n]+", " ");
            }
            log.error("AI请求返回结果，url：{} ,参数:{} ,结果 :{}", url, param.replaceAll("[\r\n]+", " "), errorMsg);
        }
        if (response.isOk()) {
            return response.body();
        }
        throw new BaseException("获取AI服务失败");
    }

    private static String execute(Map<String, Object> params, HttpRequest request, String url) {
        if (params != null && !params.isEmpty()) {
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                request.form(entry.getKey(), entry.getValue());
            }
        }

        request.timeout(10000);
        HttpResponse response = request.execute();
        if (response.isOk()) {
            return response.body();
        }
        log.info("{} Request failed, status code: {}", url, response.getStatus());
        throw new BaseException("获取服务失败");
    }

    public static <T> String put(String url, List<T> body) {
        return put(url, new Gson().toJson(body));
    }

    public static String put(String url, String body) {
        try {
            return HttpRequest.put(url)
                    .body(body)
                    .timeout(10000)
                    .execute().body();
        } catch (Exception e) {
            CommonResponse.assertError("远程调用" + url + "失败:" + e.getMessage());
            return null;
        }
    }


}
