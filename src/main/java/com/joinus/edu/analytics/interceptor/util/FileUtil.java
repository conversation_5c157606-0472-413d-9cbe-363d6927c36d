package com.joinus.edu.analytics.interceptor.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

public class FileUtil {
    private static final Logger logger = LoggerFactory.getLogger(FileUtil.class);

    public static List<String> readFileAsStream(String fileName) {
        List<String> list = new ArrayList<String>();
        BufferedReader reader = null;
        InputStream stream = null;
        try {
            stream = FileUtil.class.getResourceAsStream("/" + fileName);
            if (stream == null) {
                return null;
            }
            reader = new BufferedReader(new InputStreamReader(stream, "UTF-8"));
            String line;
            while ((line = reader.readLine()) != null) {
                if (!"".equals(line)) {
                    list.add(line);
                }
            }
        } catch (Exception e) {
            logger.error("readFile", e);
        } finally {
            try {
                if (reader != null) {
                    reader.close();
                }
            } catch (IOException e) {
                logger.error("InputStream关闭异常", e);
            }
            try {
                if (stream != null) {
                    stream.close();
                }
            } catch (IOException e) {
                logger.error("InputStream关闭异常", e);
            }
        }
        return list;
    }
}
