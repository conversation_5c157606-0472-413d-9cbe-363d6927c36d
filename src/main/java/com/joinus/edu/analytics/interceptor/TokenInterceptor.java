package com.joinus.edu.analytics.interceptor;

import com.joinus.edu.analytics.interceptor.util.FileUtil;
import com.joinus.edu.analytics.interceptor.util.WebUtil;
import com.joinus.edu.analytics.model.global.CommonResponse;
import com.joinus.edu.analytics.model.result.AccountLoginResult;
import com.joinus.edu.analytics.service.TokenService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.List;

/**
 * Token拦截器
 */
@Slf4j
@Component
public class TokenInterceptor implements HandlerInterceptor {

    @Resource
    private TokenService tokenService;

    // 白名单
    private List<String> whiteUrls;
    private int _size = 0;

    public TokenInterceptor() {
        whiteUrls = FileUtil.readFileAsStream("white/tokenWhite.txt");
        _size = null == whiteUrls ? 0 : whiteUrls.size();
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        log.info("TokenInterceptor拦截请求: {}", request.getRequestURI());

        String url = request.getRequestURI();
        if (WebUtil.isWhiteRequest(url, _size, whiteUrls) || url.contains("doc.html")) {
            return HandlerInterceptor.super.preHandle(request, response, handler);
        }

        // 获取token
        String token = extractToken(request);

        // 验证token并获取用户信息
        AccountLoginResult.DataDTO user = tokenService.validateTokenAndGetUser(token);
        if (user == null) {
            log.error("无效的token或token已过期");
            CommonResponse.assertError(CommonResponse.LOGIN_ERROR);
            return false;
        }

        // 将用户ID存入请求属性，供后续使用
        request.setAttribute("userId", user.getId());
        request.setAttribute("user", user);

        // 更新token有效期（可选）
        tokenService.refreshToken(String.valueOf(user.getId()));

        return true;
    }

    /**
     * 从请求中提取token
     */
    private String extractToken(HttpServletRequest request) {
        // 从请求头中获取token
        String token = request.getHeader("Authorization");

        // 如果请求头中没有token，则从请求参数中获取
        if (!StringUtils.hasText(token)) {
            token = request.getParameter("token");
        }

        // 如果token为空，抛出异常
        if (!StringUtils.hasText(token)) {
            log.error("请求未携带有效token");
            CommonResponse.assertError(CommonResponse.LOGIN_ERROR);
        }

        // 去掉Bearer前缀（如果有）
        if (token.startsWith("Bearer ")) {
            token = token.substring(7);
        }

        return token;
    }
}
