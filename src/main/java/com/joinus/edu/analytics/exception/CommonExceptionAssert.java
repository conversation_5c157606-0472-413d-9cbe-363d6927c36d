package com.joinus.edu.analytics.exception;


import java.text.MessageFormat;

/**
 * <pre>
 *
 * </pre>
 *
 * <AUTHOR>
 * @date 2019/5/2
 */
public interface CommonExceptionAssert extends IResponseException, Assert {

    @Override
    default BaseException newException(Object... args) {
        String msg = MessageFormat.format(this.getMsg(), args);
        return new BusinessException(this, args, msg);
    }

    @Override
    default BaseException newException(Throwable t, Object... args) {
        String msg = MessageFormat.format(this.getMsg(), args);
        return new BusinessException(this, args, msg, t);
    }

    @Override
    default BaseException newException(String message){
        return new BusinessException(this, null, message);
    }
}
