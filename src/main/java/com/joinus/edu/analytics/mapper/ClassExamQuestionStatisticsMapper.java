package com.joinus.edu.analytics.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.edu.analytics.model.dto.ClassQuestionStatisticsDto;
import com.joinus.edu.analytics.model.entity.ClassExamQuestionStatistics;
import com.joinus.edu.analytics.model.enums.QuestionAnalysisFilterEnum;
import com.joinus.edu.analytics.model.param.QuestionAnalysisParam;
import com.joinus.edu.analytics.model.result.ClassKnowledgePointAnalyticsResult;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.UUID;

/**
 * u73edu7ea7u8003u8bd5u9898u76eeu7edfu8ba1Mapperu63a5u53e3
 */
@Mapper
public interface ClassExamQuestionStatisticsMapper extends BaseMapper<ClassExamQuestionStatistics> {

    /**
     * u83b7u53d6u73edu7ea7u5e73u5747u9519u9898u6570
     *
     * @param classIds u73edu7ea7IDu5217u8868
     * @param examId   u8003u8bd5ID
     * @return u5e73u5747u9519u9898u6570
     */
    Integer getAvgWrongQuestionsByClassAndExam(@Param("classIds") List<Long> classIds, @Param("examId") UUID examId);

    IPage<ClassQuestionStatisticsDto> getClassQuestionStatistics(IPage<ClassQuestionStatisticsDto> pageParameters, @Param("param") QuestionAnalysisParam param);

    IPage<ClassQuestionStatisticsDto> getStudentQuestionStatistics(IPage<ClassQuestionStatisticsDto> pageParameters, @Param("param") QuestionAnalysisParam param);
}
