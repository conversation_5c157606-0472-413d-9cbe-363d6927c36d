package com.joinus.edu.analytics.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.edu.analytics.model.dto.AllExamClassInfoDto;
import com.joinus.edu.analytics.model.dto.ExamDetailsDto;
import com.joinus.edu.analytics.model.dto.RecentExamDTO;
import com.joinus.edu.analytics.model.entity.ClassExamStatistics;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * @Description: 班级考试统计Mapper
 * @Author: anpy
 * @date: 2025/4/9
 */
@Mapper
public interface ClassExamStatisticsMapper extends BaseMapper<ClassExamStatistics> {

    /**
     * 获取试卷详情
     * @param examId 试卷id
     * @param classId 班级id
     *
     * @return 试卷详情
     */
    ExamDetailsDto getExamDetails(@Param("examId") UUID examId, @Param("classId") Long classId);

    List<AllExamClassInfoDto> getAllExamClassInfo(@Param("gradeId") Long gradeId);

    /**
     * 获取一个月内的考试统计列表
     * @param page 分页数据
     * @param classIds 班级id列表
     * @param oneMonthAgo 一个月之前的时间
     * @return 考试统计列表
     */
    IPage<RecentExamDTO> getExamStatisticsList(Page<RecentExamDTO> page, @Param("classIds") List<Long> classIds, @Param("oneMonthAgo") Date oneMonthAgo);
    
    /**
     * 获取一个月内的不重复考试ID列表
     * @param classIds 班级ID列表
     * @param oneMonthAgo 一个月前的时间
     * @return 不重复的考试ID列表
     */
    List<UUID> getDistinctExamIds(@Param("classIds") List<Long> classIds, @Param("oneMonthAgo") Date oneMonthAgo);
    
    /**
     * 根据考试ID列表获取班级考试统计数据
     * @param classIds 班级ID列表
     * @param examIds 考试ID列表
     * @param oneMonthAgo 一个月前的时间
     * @return 班级考试统计数据列表
     */
    List<ClassExamStatistics> getExamStatisticsByExamIds(
            @Param("classIds") List<Long> classIds, 
            @Param("examIds") List<UUID> examIds, 
            @Param("oneMonthAgo") Date oneMonthAgo);
}
