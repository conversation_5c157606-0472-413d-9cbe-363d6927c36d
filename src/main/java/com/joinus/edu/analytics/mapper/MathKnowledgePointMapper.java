package com.joinus.edu.analytics.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.edu.analytics.model.entity.MathKnowledgePoint;
import com.joinus.edu.analytics.model.po.MathKnowledgePointPo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_knowledge_points】的数据库操作Mapper
* @createDate 2025-02-28 14:12:06
* @Entity com.joinus.knowledge.entity.MathKnowledgePoints
*/
public interface MathKnowledgePointMapper extends BaseMapper<MathKnowledgePoint> {

    List<MathKnowledgePointPo> listByIds(@Param("knowledgePointIds") List<UUID> kpIds);
}




