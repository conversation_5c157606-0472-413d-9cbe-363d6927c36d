package com.joinus.edu.analytics.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.edu.analytics.model.entity.ClassKnowledgePoint;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * u73edu7ea7u77e5u8bc6u70b9u5206u6790Mapperu63a5u53e3
 * 
 * <AUTHOR>
 * @date 2025/4/8
 */
@Mapper
public interface ClassKnowledgePointMapper extends BaseMapper<ClassKnowledgePoint> {
    
    /**
     * u83b7u53d6u73edu7ea7u8584u5f31u77e5u8bc6u70b9u5206u6790
     * 
     * @param examId u8003u8bd5ID
     * @param classId u73edu7ea7ID
     * @return u77e5u8bc6u70b9u5206u6790u5217u8868
     */
    List<Map<String, Object>> getWeakKnowledgeAnalysis(@Param("examId") UUID examId, 
                                                     @Param("classId") Long classId);
    
    /**
     * u83b7u53d6u77e5u8bc6u70b9u8be6u7ec6u5206u6790u6570u636e
     * 
     * @param examId u8003u8bd5ID
     * @param classId u73edu7ea7ID
     * @return u77e5u8bc6u70b9u5206u6790u6570u636eu5217u8868
     */
    List<Map<String, Object>> getKnowledgePointAnalysis(@Param("examId") UUID examId, 
                                                      @Param("classId") Long classId);
}
