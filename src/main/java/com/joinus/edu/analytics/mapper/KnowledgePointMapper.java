package com.joinus.edu.analytics.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.edu.analytics.model.dto.ExamDetailsDto;
import com.joinus.edu.analytics.model.dto.WeakKnowledgePointDTO;
import com.joinus.edu.analytics.model.param.KnowledgePointAnalyticeParam;
import com.joinus.edu.analytics.model.result.ClassKnowledgePointAnalyticsResult;
import com.joinus.edu.analytics.model.result.StudentKnowledgePointAnalyticsDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * @Description: 知识点相关数据库操作
 * @Author: anpy
 * @date: 2025/4/7
 */
@Mapper
public interface KnowledgePointMapper {

    /**
     * 获取参与考试的学生数量
     *
     * @param classIds 班级ID列表
     * @return 学生数量
     */
    Integer countStudentsByClassIds(@Param("classIds") List<Long> classIds);
    
    /**
     * 获取知识点统计数据
     *
     * @param classIds 班级ID列表
     * @return 知识点统计数据列表
     */
    List<Map<String, Object>> getKnowledgePointStats(@Param("classIds") List<Long> classIds);

    /**
     * 知识点详细分析
     */
    IPage<ClassKnowledgePointAnalyticsResult> getKnowledgePointAnalysis(IPage<ClassKnowledgePointAnalyticsResult> pageParameters, @Param("param") KnowledgePointAnalyticeParam param);

    IPage<StudentKnowledgePointAnalyticsDto> getStudentKnowledgePointAnalysis(IPage<StudentKnowledgePointAnalyticsDto> pageParameters, @Param("param") KnowledgePointAnalyticeParam param);

    /**
     * 获取学生知识点掌握度详情（包含题号、掌握度、班级正确率等信息）
     * 一次性查询所有相关数据，减少数据库访问次数
     *
     * @param pageParameters 分页参数
     * @param param 查询参数
     * @return 知识点分析结果
     */
    IPage<StudentKnowledgePointAnalyticsDto> getStudentKnowledgePointAnalysisDetail(IPage<StudentKnowledgePointAnalyticsDto> pageParameters, @Param("param") KnowledgePointAnalyticeParam param);

    List<StudentKnowledgePointAnalyticsDto> getStudentKnowledgePointDetail(@Param("param") KnowledgePointAnalyticeParam param);
}
