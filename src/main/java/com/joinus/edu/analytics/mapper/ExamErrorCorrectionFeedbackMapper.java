package com.joinus.edu.analytics.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.edu.analytics.model.entity.ExamErrorCorrectionFeedback;
import com.joinus.edu.analytics.model.param.ExamErrorCorrectionFeedbackParam;
import com.joinus.edu.analytics.model.result.ExamErrorCorrectionFeedbackPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 试卷纠错Mapper
 */
@Mapper
public interface ExamErrorCorrectionFeedbackMapper extends BaseMapper<ExamErrorCorrectionFeedback> {

    /**
     * 获取试卷纠错分页
     * @param page
     * @param param
     * @return
     */
    IPage<ExamErrorCorrectionFeedbackPo> getExamErrorCorrectionFeedbackList(Page<ExamErrorCorrectionFeedbackPo> page, @Param("vo") ExamErrorCorrectionFeedbackParam param);

    /**
     * 根据学生id查询学生基本信息
     * @param studentId
     * @return
     */
    ExamErrorCorrectionFeedbackPo selectStudentInfoByStudentId(@Param("studentId") Long studentId);
}
