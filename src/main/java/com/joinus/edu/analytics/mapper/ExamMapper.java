package com.joinus.edu.analytics.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.edu.analytics.model.dto.ExamInfoDto;
import com.joinus.edu.analytics.model.entity.ExamAnalyzeResult;
import com.joinus.edu.analytics.model.entity.MathExams;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.UUID;

@Mapper
public interface ExamMapper extends BaseMapper<MathExams> {

    List<ExamInfoDto> getExamInfo(UUID examId);

}
