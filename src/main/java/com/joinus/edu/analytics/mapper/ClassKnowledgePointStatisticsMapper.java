package com.joinus.edu.analytics.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.edu.analytics.model.dto.WeakKnowledgePointDTO;
import com.joinus.edu.analytics.model.entity.ClassKnowledgePointStatistics;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.UUID;

/**
 * @Description: 班级知识点统计Mapper接口
 * @Author:  anpy
 * @date:  2025/4/16 16:25
 */
@Mapper
public interface ClassKnowledgePointStatisticsMapper extends BaseMapper<ClassKnowledgePointStatistics> {
    
    /**
     * 获取班级薄弱知识点列表
     *
     * @param classIds  班级ID列表
     * @param threshold 正确率阈值
     * @param limit     返回数量限制
     * @param examId
     * @return 薄弱知识点列表
     */
    List<WeakKnowledgePointDTO> getWeakKnowledgePoints(
            @Param("classIds") List<Long> classIds,
            @Param("threshold") double threshold,
            @Param("limit") int limit, @Param("examId") UUID examId);
}
