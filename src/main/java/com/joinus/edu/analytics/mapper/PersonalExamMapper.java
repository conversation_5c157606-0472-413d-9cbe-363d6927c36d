package com.joinus.edu.analytics.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.joinus.edu.analytics.model.dto.PersonalExamDto;
import com.joinus.edu.analytics.model.entity.PersonalExam;
import com.joinus.edu.analytics.model.param.StudentInfoParam;
import org.apache.ibatis.annotations.Mapper;

/**
 * 个人试卷Mapper接口
 */
@Mapper
public interface PersonalExamMapper extends BaseMapper<PersonalExam> {

    IPage<PersonalExamDto> getStudentExamPage(IPage<PersonalExamDto> page, StudentInfoParam param);
}
