package com.joinus.edu.analytics.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.edu.analytics.model.entity.MathExams;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.UUID;

/**
 * u8bd5u5377Mapperu63a5u53e3
 */
@Mapper
public interface MathExamsMapper extends BaseMapper<MathExams> {
    
    /**
     * u6839u636eu8bd5u5377IDu5217u8868u83b7u53d6u8bd5u5377u4fe1u606f
     * 
     * @param examIds u8bd5u5377IDu5217u8868
     * @return u8bd5u5377u4fe1u606fu5217u8868
     */
    List<MathExams> getExamsByIds(@Param("examIds") List<UUID> examIds);
}
