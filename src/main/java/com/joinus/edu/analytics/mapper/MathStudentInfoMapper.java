package com.joinus.edu.analytics.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.joinus.edu.analytics.model.dto.StudentInfoPlanDto;
import com.joinus.edu.analytics.model.entity.MathStudentInfo;
import com.joinus.edu.analytics.model.param.StudentInfoParam;

/**
* @description 针对表【math_student_info(数学学生信息表)】的数据库操作Mapper
*/
public interface MathStudentInfoMapper extends BaseMapper<MathStudentInfo> {

    IPage<StudentInfoPlanDto> getStudentPlanPage(IPage<StudentInfoPlanDto> page, StudentInfoParam param);

    Boolean updateStudentPlanStatus(Long studentId, Boolean studyPlanEnabled);
}




