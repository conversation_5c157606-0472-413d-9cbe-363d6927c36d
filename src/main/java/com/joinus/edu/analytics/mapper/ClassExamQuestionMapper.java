package com.joinus.edu.analytics.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.edu.analytics.model.entity.ClassExamQuestion;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * u73edu7ea7u8bd5u5377u9898u76eeu5206u6790Mapperu63a5u53e3
 * 
 * <AUTHOR>
 * @date 2025/4/8
 */
@Mapper
public interface ClassExamQuestionMapper extends BaseMapper<ClassExamQuestion> {
    
    /**
     * u83b7u53d6u73edu7ea7u9ad8u9891u9519u9898TOP N
     * 
     * @param examId u8003u8bd5ID
     * @param classId u73edu7ea7ID
     * @param limit u9650u5236u6570u91cf
     * @return u9519u9898u5217u8868
     */
    List<Map<String, Object>> getTopWrongQuestions(@Param("examId") UUID examId, 
                                                  @Param("classId") Long classId, 
                                                  @Param("limit") Integer limit);
    
    /**
     * u83b7u53d6u73edu7ea7u9898u76eeu5206u6790u6570u636e
     * 
     * @param examId u8003u8bd5ID
     * @param classId u73edu7ea7ID
     * @param questionType u9898u76eeu7c7bu578buff08u53efu9009uff09
     * @return u9898u76eeu5206u6790u6570u636eu5217u8868
     */
    List<Map<String, Object>> getQuestionAnalysis(@Param("examId") UUID examId, 
                                                @Param("classId") Long classId, 
                                                @Param("questionType") String questionType);
}
