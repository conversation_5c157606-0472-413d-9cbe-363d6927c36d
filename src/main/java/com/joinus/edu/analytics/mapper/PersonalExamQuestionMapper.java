package com.joinus.edu.analytics.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.edu.analytics.model.entity.PersonalExamQuestion;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 个人试卷做题结果Mapper接口
 */
@Mapper
public interface PersonalExamQuestionMapper extends BaseMapper<PersonalExamQuestion> {
    
    /**
     * 根据个人试卷ID统计错题数量
     * @param personalExamId 个人试卷ID
     * @return 错题数量
     */
    Integer countWrongQuestionsByPersonalExamId(@Param("personalExamId") Long personalExamId);
    
    /**
     * 根据个人试卷ID计算正确率
     * @param personalExamId 个人试卷ID
     * @return 正确率（0-1之间的小数）
     */
    Double calculateCorrectRateByPersonalExamId(@Param("personalExamId") Long personalExamId);
}
