package com.joinus.edu.analytics.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.edu.analytics.model.dto.RecentExamDTO;
import com.joinus.edu.analytics.model.dto.StudentAnalyticsDto;
import com.joinus.edu.analytics.model.entity.ClassStudentExamResultEntity;
import com.joinus.edu.analytics.model.entity.ExamAnalyzeResult;
import com.joinus.edu.analytics.model.entity.PersonalExamQuestion;
import com.joinus.edu.analytics.model.param.RecentExamAnalyticsParam;
import com.joinus.edu.analytics.model.param.StudentAnalyticsParam;
import com.joinus.edu.analytics.model.param.StudentInfoParam;
import com.joinus.edu.analytics.model.po.MathPersonalExamKnowledgePointPo;
import com.joinus.edu.analytics.model.result.RecentExamAnalyticsResult;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * @Description:
 * @Author: anpy
 * @date: 2025/4/7 15:38
 */
@Mapper
public interface ExamAnalyzeResultMapper extends BaseMapper<ExamAnalyzeResult> {

    /**
     * @param classIds
     * @return 10
     */
    int countExamsWithMoreThan10StudentsByClass(@Param("classIds") List<Long> classIds);

    /**
     * 10
     *
     * @return
     */
    List<UUID> getRecentExamsWithMoreThan10Students();

    /**
     * 10(10)
     *
     * @param pageNum
     * @param pageSize
     * @return
     */
    List<String> getRecentExamsWithMoreThan10StudentsPaged(@Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    /**
     * 10
     *
     * @return
     */
    Integer countRecentExamsWithMoreThan10Students();

    /**
     * @param examId
     * @return
     */
    RecentExamDTO getExamBasicInfo(@Param("examId") UUID examId);

    /**
     * @param examId
     * @return
     */
    Map<String, Object> getExamParticipationInfo(@Param("examId") UUID examId);

    /**
     * (10)
     *
     * @param examId
     * @param classId
     * @return
     */
    Map<String, Object> getExamParticipationInfoByClass(@Param("examId") UUID examId, @Param("classId") Long classId);

    /**
     * @param examId
     * @return
     */
    Double getExamCorrectRate(@Param("examId") UUID examId);

    /**
     * (10)
     *
     * @param examId
     * @param classId
     * @return
     */
    Double getExamCorrectRateByClass(@Param("examId") UUID examId, @Param("classId") Long classId);

    /**
     * @param examId
     * @return
     */
    Integer getExamAvgWrongQuestions(@Param("examId") UUID examId);

    /**
     * (10)
     *
     * @param examId
     * @param classId
     * @return
     */
    Integer getExamAvgWrongQuestionsByClass(@Param("examId") UUID examId, @Param("classId") Long classId);

    /**
     * @param examId
     * @return
     */
    Integer getExamWeakKnowledgePoints(@Param("examId") UUID examId);

    /**
     * (10)
     *
     * @param examId
     * @param classId
     * @return
     */
    Integer getExamWeakKnowledgePointsByClass(@Param("examId") UUID examId, @Param("classId") Long classId);

    /**
     * @param examId
     * @return (10)
     */
    List<Map<String, Object>> getExamClassList(@Param("examId") UUID examId);

    /**
     * @param classId
     * @return
     */
    Integer getClassTotalStudents(@Param("classId") Long classId);

    /**
     * u83b7u53d6u73edu7ea7u6240u6709u5b66u751fuff08u5206u9875uff09
     *
     * @param page    u5206u9875u53c2u6570
     * @param classId u73edu7ea7ID
     * @return u5b66u751fu5217u8868uff08u5305u542bu5b66u751fIDu3001u59d3u540du7b49u4fe1u606fuff09
     */
    IPage<Map<String, Object>> getClassStudents(Page<?> page, @Param("classId") Long classId);

    /**
     * 获取班级下学生（分页），包含是否有考试结果
     *
     * @param page  分页页码
     * @param param classId 班级ID
     *              examId 考试ID
     * @return 学生考情分析报告
     */
    IPage<ClassStudentExamResultEntity> getClassStudentsWithExamResults(Page<?> page, @Param("param") StudentAnalyticsParam param);

    StudentAnalyticsDto studentInfo(@Param("param") StudentInfoParam param);

    /**
     * 根据考情分析报告id获取题目列表
     *
     * @param analyzeId 考情分析报告id
     */
    List<PersonalExamQuestion> questionList(@Param("analyzeId") Long analyzeId);

    /**
     * 一个月内人数超过10的试卷
     *
     * @param page             分页对象
     * @param classIds         班级id列表
     * @param studentThreshold 学生阈值
     * @param oneMonthAgo      一个月前
     * @return 试卷分页列表
     */
    IPage<RecentExamDTO> getRecentExamsPage(Page<RecentExamDTO> page,
                                            @Param("classIds") List<Long> classIds,
                                            @Param("studentThreshold") int studentThreshold,
                                            @Param("oneMonthAgo") Date oneMonthAgo);

    IPage<RecentExamAnalyticsResult> selectRecentExamAnalytics(Page<Object> page, @Param("param") RecentExamAnalyticsParam param);

    List<MathPersonalExamKnowledgePointPo> listWeakKnowledgePointsByExamAnalyzeResultIds(@Param("examAnalyzeResultIds") List<Long> examAnalyzeResultIds);
}
