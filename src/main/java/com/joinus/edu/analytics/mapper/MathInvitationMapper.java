package com.joinus.edu.analytics.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.edu.analytics.model.entity.MathInvitation;
import com.joinus.edu.analytics.model.param.MathInvitationParam;
import com.joinus.edu.analytics.model.result.MathInvitationPo;
import org.apache.ibatis.annotations.Param;

public interface MathInvitationMapper extends BaseMapper<MathInvitation> {

    IPage<MathInvitationPo> selectMathInvitationList(Page<MathInvitationPo> page, @Param("vo")MathInvitationParam param);
}




