package com.joinus.edu.analytics.controller;

import com.joinus.edu.analytics.model.global.ApiResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/test")
@Tag(name = "测试接口", description = "测试接口说明")
public class TestController {

    @Operation(summary = "测试接口", description = "测试接口说明")
    @GetMapping
    public ApiResult<String> hello() {
        return ApiResult.success("hello");
    }
}
