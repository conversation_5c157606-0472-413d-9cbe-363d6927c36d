package com.joinus.edu.analytics.controller;

import com.joinus.edu.analytics.model.global.ApiResult;
import com.joinus.edu.analytics.model.result.ExamInfoResult;
import com.joinus.edu.analytics.service.ExamService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;

@RestController
@RequestMapping("/exam")
public class ExamController {

    @Resource
    private ExamService examService;

    @GetMapping
    public ApiResult<ExamInfoResult> getExamInfo(@RequestParam UUID examId) {

        return ApiResult.success(examService.getExamInfo(examId));
    }

}
