package com.joinus.edu.analytics.controller;

import com.joinus.edu.analytics.service.RestTemplateForwardingService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.io.IOException;
import java.io.InputStream;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * 使用RestTemplate实现的请求转发控制器
 * 处理所有以/api/qyl-manager开头的请求，并将它们转发到目标服务
 */
@Slf4j
@Controller
@RequiredArgsConstructor
@RequestMapping("/api/qyl-manager")
public class RestForwardingController {

    private final RestTemplateForwardingService forwardingService;

    /**
     * 处理所有请求并转发
     *
     * @param request  HTTP请求
     * @param response HTTP响应
     * @throws IOException 如果发生I/O错误
     */
    @RequestMapping(value = "/**", method = {RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.DELETE})
    public void forwardRequest(HttpServletRequest request, HttpServletResponse response) throws IOException {
        // 获取请求路径
        String requestURI = request.getRequestURI();
        log.info("原始请求URI: {}", requestURI);
        
        // 定位需要截取的前缀位置
        String contextPath = request.getContextPath(); // 获取应用的上下文路径
        String prefix = "/api/qyl-manager";
        String fullPrefix = contextPath + prefix;
        
        log.info("应用上下文路径: {}", contextPath);
        log.info("完整前缀: {}", fullPrefix);
        
        // 先尝试完整前缀
        int prefixIndex = requestURI.indexOf(fullPrefix);
        if (prefixIndex == -1) {
            // 如果完整前缀不匹配，尝试仅匹配基本前缀
            prefixIndex = requestURI.indexOf(prefix);
            if (prefixIndex == -1) {
                log.error("无法在URI中找到前缀: {} 或 {}", fullPrefix, prefix);
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write("无效的请求路径");
                return;
            } else {
                // 使用基本前缀
                log.info("使用基本前缀: {}", prefix);
                fullPrefix = prefix;
            }
        } else {
            log.info("使用完整前缀: {}", fullPrefix);
        }
        
        // 从原始路径中提取出相对路径部分
        String relativePath = requestURI.substring(prefixIndex + fullPrefix.length());
        log.info("相对路径: {}", relativePath);
        
        // 如果相对路径不以/开头，添加/
        if (!relativePath.startsWith("/") && !relativePath.isEmpty()) {
            relativePath = "/" + relativePath;
        }
        
        // 将路径替换为目标服务的路径格式
        String targetPath = "/api/edu-knowledge-hub" + relativePath;
        log.info("目标路径: {}", targetPath);
        
        // 记录转发请求
        log.info("转发请求: {} {} -> {}", request.getMethod(), requestURI, targetPath);
        
        // 获取请求头
        Map<String, String> headers = getRequestHeaders(request);
        
        // 获取查询参数
        Map<String, List<String>> queryParams = getQueryParams(request);
        
        // 获取HTTP方法
        HttpMethod method = HttpMethod.valueOf(request.getMethod());
        
        // 根据请求方法处理请求体
        Object requestBody = null;
        if (HttpMethod.POST.equals(method) || HttpMethod.PUT.equals(method)) {
            if (isFormRequest(request)) {
                // 处理表单请求
                Map<String, List<String>> formData = getFormData(request);
                requestBody = forwardingService.convertToMultiValueMap(formData);
            } else {
                // 处理JSON或其他类型的请求
                try (InputStream inputStream = request.getInputStream()) {
                    requestBody = StreamUtils.copyToByteArray(inputStream);
                }
            }
        }
        
        // 执行请求转发
        ResponseEntity<byte[]> responseEntity = forwardingService.forwardRequest(
                method, targetPath, headers, queryParams, requestBody);
        
        // 处理响应
        response.setStatus(responseEntity.getStatusCodeValue());
        
        // 设置响应头
        responseEntity.getHeaders().forEach((name, values) -> {
            values.forEach(value -> {
                if (!name.equalsIgnoreCase("Content-Length")) {
                    response.addHeader(name, value);
                }
            });
        });
        
        // 设置响应体
        byte[] body = responseEntity.getBody();
        if (body != null) {
            response.getOutputStream().write(body);
        }
    }
    
    /**
     * 获取请求头
     *
     * @param request HTTP请求
     * @return 请求头映射
     */
    private Map<String, String> getRequestHeaders(HttpServletRequest request) {
        Map<String, String> headers = new HashMap<>();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            headers.put(headerName, request.getHeader(headerName));
        }
        return headers;
    }

    /**
     * 获取查询参数
     *
     * @param request HTTP请求
     * @return 查询参数映射
     */
    private Map<String, List<String>> getQueryParams(HttpServletRequest request) {
        Map<String, List<String>> queryParams = new HashMap<>();
        request.getParameterMap().forEach((key, values) -> {
            if (values.length > 0) {
                List<String> valueList = Arrays.asList(values);
                queryParams.put(key, valueList);
            }
        });
        return queryParams;
    }

    /**
     * 获取表单数据
     *
     * @param request HTTP请求
     * @return 表单数据映射
     */
    private Map<String, List<String>> getFormData(HttpServletRequest request) {
        Map<String, List<String>> formData = new HashMap<>();
        request.getParameterMap().forEach((key, values) -> {
            if (values.length > 0) {
                List<String> valueList = Arrays.asList(values);
                formData.put(key, valueList);
            }
        });
        return formData;
    }

    /**
     * 判断是否是表单请求
     *
     * @param request HTTP请求
     * @return 是否是表单请求
     */
    private boolean isFormRequest(HttpServletRequest request) {
        String contentType = request.getContentType();
        return contentType != null && contentType.contains("application/x-www-form-urlencoded");
    }
}
