package com.joinus.edu.analytics.controller.offlinestore;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.joinus.edu.analytics.model.dto.PersonalExamDto;
import com.joinus.edu.analytics.model.dto.StudentInfoPlanDto;
import com.joinus.edu.analytics.model.enums.BookVolumeEnum;
import com.joinus.edu.analytics.model.enums.PublisherEnum;
import com.joinus.edu.analytics.model.global.ApiResult;
import com.joinus.edu.analytics.model.param.CreateStudentPlanParam;
import com.joinus.edu.analytics.model.param.ExamAnalyzeResultParam;
import com.joinus.edu.analytics.model.global.CommonResponse;
import com.joinus.edu.analytics.model.param.StudentInfoParam;
import com.joinus.edu.analytics.model.param.PreviewStudentPlanParam;
import com.joinus.edu.analytics.model.result.ExamAnalyzeResultResult;
import com.joinus.edu.analytics.model.result.MathBookNodeResult;
import com.joinus.edu.analytics.model.result.MathCatalogNodeResult;
import com.joinus.edu.analytics.model.result.MathStudentPlanResult;
import com.joinus.edu.analytics.service.*;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@RestController
@RequestMapping("/offline-store/study-plan")
@RequiredArgsConstructor
public class StudyPlanController {

    @Autowired
    private StudyPlanService studyPlanService;
    @Autowired
    private MathStudentInfoService mathStudentInfoService;
    @Autowired
    private PersonalExamService personalExamService;
    @Autowired
    private StudentExamAnalyticsService studentExamAnalyticsService;
    @Autowired
    private MathStudentStudyPlanService mathStudentStudyPlanService;

    @Operation(summary = "知识点树(根据教材)", description = "根据教材版本、年级、册获取知识点树信息")
    @GetMapping("/knowledge-points/tree")
    public ApiResult<List<MathCatalogNodeResult>> listKnowlwdgePointsTree(@RequestParam PublisherEnum publisher,
                                                                    @RequestParam Integer grade,
                                                                    @RequestParam BookVolumeEnum bookVolume) {
        List<MathCatalogNodeResult> results = studyPlanService.listKnowlwdgePointsTree(publisher, grade, bookVolume);
        return ApiResult.success(results);
    }

    @Operation(summary = "查询教材信息", description = "查询教材信息")
    @GetMapping("/books")
    public ApiResult listBooksTree() {
        List<JSONObject> results = studyPlanService.listBooksTree();

        return ApiResult.success(results);
    }


    @Operation(summary = "分页条件查询考情分析报告列表", description = "根据考情分析报告获取知识点树信息")
    @GetMapping("/exam-analyze-results")
    public ApiResult<IPage<ExamAnalyzeResultResult>> listExamAnalyzeResultsByStudentId(ExamAnalyzeResultParam param) {

        IPage<ExamAnalyzeResultResult> page = studentExamAnalyticsService.pageExamAnalyzeResults(param);
        return ApiResult.success(page);
    }

    @Operation(summary = "知识点树(根据考情分析报告)", description = "根据考情分析报告获取知识点树信息")
    @GetMapping("/knowledge-points/tree/exam-analyze-results")
    public ApiResult<List<MathBookNodeResult>> listKnowlwdgePointsTreeByExamAnalyzeResultId(@RequestParam List<Long> examAnalyzeResultIds) {
        List<MathBookNodeResult> results = studyPlanService.listKnowlwdgePointsTreeByExamAnalyzeResultIds(examAnalyzeResultIds);
        return ApiResult.success(results);
    }

    @Operation(summary = "查询学生学习计划列表", description = "查询学生学习计划列表")
    @GetMapping("/students/plans")
    public ApiResult listStudentPlanPage(StudentInfoParam param){
        return ApiResult.success(mathStudentStudyPlanService.listByStudentId(param.getStudentId()));
    }

    @Operation(summary = "预览学生学习计划状态", description = "预览学生学习计划状态")
    @PostMapping("/students/plans/preview")
    public ApiResult<List<MathStudentPlanResult>> previewStudentPlan(@RequestBody PreviewStudentPlanParam param){
        List<MathStudentPlanResult> planKnowledgePoints = studyPlanService.previewStudentPlan(param);
        return ApiResult.success(planKnowledgePoints);
    }

    @Operation(summary = "创建学生学习计划状态", description = "创建学生学习计划状态")
    @PostMapping("/students/plans")
    public ApiResult<List<MathStudentPlanResult>> createStudentPlan(@RequestBody CreateStudentPlanParam param){
        List<MathStudentPlanResult> planKnowledgePoints = studyPlanService.createStudentPlan(param);
        return ApiResult.success(planKnowledgePoints);
    }


    @Operation(summary = "修改学生学习计划状态", description = "修改学生学习计划状态")
    @PutMapping("/students/plans/change")
    public ApiResult<Boolean> changeStudentPlanStatus(@RequestBody StudentInfoParam param){
        CommonResponse.ERROR.assertNotNull(param.getStudentId(), "学生id参数错误");
        CommonResponse.ERROR.assertNotNull(param.getStudyPlanEnabled(), "修改状态参数错误");
        return ApiResult.success(mathStudentInfoService.changeStudentPlanStatus(param.getStudentId(), param.getStudyPlanEnabled()));
    }

    @Operation(summary = "获取学生学习记录", description = "获取学生学习记录")
    @GetMapping("/students/exam/page")
    public ApiResult<IPage<PersonalExamDto>> getStudentPlanPage(StudentInfoParam param) {
        return ApiResult.success(personalExamService.getStudentExamPage(param));
    }
}
