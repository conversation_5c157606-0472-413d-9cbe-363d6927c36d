package com.joinus.edu.analytics.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.joinus.edu.analytics.model.global.ApiResult;
import com.joinus.edu.analytics.model.param.ExamErrorCorrectionFeedbackParam;
import com.joinus.edu.analytics.model.result.ExamErrorCorrectionFeedbackPo;
import com.joinus.edu.analytics.service.ExamErrorCorrectionFeedbackService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Description: 试卷纠错反馈后台
 */
@RestController
@RequestMapping("/api/exam_error_correction_feedback")
@Tag(name = "试卷纠错反馈后台", description = "试卷纠错反馈后台")
@RequiredArgsConstructor
public class ExamErrorCorrectionFeedbackController {

    @Autowired
    private ExamErrorCorrectionFeedbackService examErrorCorrectionFeedbackService;

    @Operation(summary = "试卷纠错反馈后台列表", description = "试卷纠错反馈后台列表")
    @PostMapping("/page")
    public ApiResult<IPage<ExamErrorCorrectionFeedbackPo>> getRecentExamAnalytics(@RequestBody ExamErrorCorrectionFeedbackParam param) {
        IPage<ExamErrorCorrectionFeedbackPo> result = examErrorCorrectionFeedbackService.getExamCorrectionResultList(param);
        return ApiResult.success(result);
    }

}
