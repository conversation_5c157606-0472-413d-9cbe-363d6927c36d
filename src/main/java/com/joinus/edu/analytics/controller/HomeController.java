package com.joinus.edu.analytics.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.joinus.edu.analytics.model.dto.GradeClassGroupDataDTO;
import com.joinus.edu.analytics.model.dto.RecentExamDTO;
import com.joinus.edu.analytics.model.dto.WeakKnowledgePointDTO;
import com.joinus.edu.analytics.model.global.ApiResult;
import com.joinus.edu.analytics.model.global.CommonResponse;
import com.joinus.edu.analytics.model.param.RecentExamParam;
import com.joinus.edu.analytics.model.result.ClassAccuracyComparisonResult;
import com.joinus.edu.analytics.service.HomeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

/**
 * @Description: 首页接口
 * @Author: anpy
 * @date: 2025/4/16 15:53
 */
@RestController
@RequestMapping("/home")
@Tag(name = "首页接口", description = "提供教师首页相关功能")
public class HomeController {

    /**
     * 首页服务
     */
    @Resource
    private HomeService homeService;

    /**
     * 获取教师关联班级
     *
     * @param teacherId 教师ID
     * @return 班级列表
     */
    @Operation(summary = "获取教师关联班级", description = "获取当前登录教师所有关联的班级列表")
    @GetMapping("/classes/by-teacher")
    public ApiResult<GradeClassGroupDataDTO> getTeacherClasses(Long teacherId) {
        // 校验教师ID不能为空
        CommonResponse.ERROR.assertNotNull(teacherId, "教师ID不能为空");
        // 调用服务获取班级列表
        return ApiResult.success(homeService.getClassAndGradeByTeacherId(teacherId));
    }

    @Operation(summary = "班级信息", description = "班级信息")
    @GetMapping("/classes/info")
    public ApiResult<GradeClassGroupDataDTO> classesInfo(@RequestParam("classIds") List<Long> classIds) {
        // 校验教师ID不能为空
        CommonResponse.ERROR.assertCollNotNull(classIds, "班级id不能为空");
        // 调用服务获取班级列表
        return ApiResult.success(homeService.classesInfo(classIds));
    }

    /**
     * 班级平均正确率对比
     *
     * @return 对比数据
     */
    @Operation(summary = "班级平均正确率对比", description = "获取各班级平均正确率数据进行对比")
    @GetMapping("/class-accuracy-comparison")
    public ApiResult<List<ClassAccuracyComparisonResult>> getClassAccuracyComparison(@RequestParam("classIds") List<Long> classIds) {
        if (CollUtil.isEmpty(classIds)) {
            return ApiResult.failed("班级不能为空");
        }
        return ApiResult.success(homeService.getClassAccuracyComparison(classIds));
    }

    /**
     * 薄弱知识点分布
     *
     * @param classIds 班级ID列表
     * @return 薄弱知识点列表
     */
    @Operation(summary = "薄弱知识点分布", description = "根据班级获取掌握度低于60%的薄弱知识点，最多返回5条")
    @GetMapping("/weak-knowledge-points")
    public ApiResult<List<WeakKnowledgePointDTO>> getWeakKnowledgePoints(@RequestParam("classIds") List<Long> classIds,
                                                                         @RequestParam("type") String type,
                                                                         @RequestParam(value = "examId" ,required = false) UUID examId) {
        CommonResponse.ERROR.assertCollNotNull(classIds, "班级ID不能为空");
        return ApiResult.success(homeService.getWeakKnowledgePoints(classIds, type,examId));
    }

    /**
     * 一个月内人数超过10的试卷
     *
     * @return 试卷列表
     */
    @Operation(summary = "一个月内人数超过10的试卷", description = "获取近一个月内参与人数超过10人的试卷列表")
    @GetMapping("/recent-exams")
    public ApiResult<IPage<RecentExamDTO>> getRecentExams(RecentExamParam param) {
        CommonResponse.ERROR.assertCollNotNull(param.getClassIds(), "班级ID不能为空");
        IPage<RecentExamDTO> page = homeService.getRecentExams(param);
        return ApiResult.success(page);
    }
}
