package com.joinus.edu.analytics.controller;

import com.joinus.edu.analytics.model.param.AccountLoginParam;
import com.joinus.edu.analytics.model.global.ApiResult;
import com.joinus.edu.analytics.model.param.VerifyParam;
import com.joinus.edu.analytics.model.result.LoginResult;
import com.joinus.edu.analytics.service.CaptchaService;
import com.joinus.edu.analytics.service.LoginService;
import com.joinus.edu.analytics.service.TokenService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

/**
 * @Description: 登录接口
 * @Author: anpy
 * @date: 2025/4/7 15:41
 */
@RestController
@RequestMapping("/login")
@Validated
@Tag(name = "登录接口", description = "提供登录相关功能")
public class LoginController {
    @Resource
    private CaptchaService captchaService;
    @Resource
    private LoginService loginService;

    @Resource
    private TokenService tokenService;

    @Operation(summary = "账号密码登录", description = "通过账号密码进行登录认证")
    @PostMapping("/account")
    public ApiResult<LoginResult> loginByAccount(@Valid @RequestBody AccountLoginParam param) {
        return ApiResult.success(loginService.accountLogin(param));
    }

    @Operation(summary = "获取验证码", description = "获取图形验证码")
    @GetMapping("/validCode")
    public void getCaptcha(HttpServletRequest request, HttpServletResponse response) throws IOException {
        loginService.getCaptcha(request, response);

    }

    @Operation(summary = "注销登录", description = "注销当前用户登录状态")
    @PostMapping("/logout")
    public ApiResult<Void> logout(HttpServletRequest request) {
        Object userId = request.getAttribute("userId");
        if (userId != null) {
            tokenService.logout(String.valueOf(userId));
        }
        return ApiResult.success();
    }


    /**
     * 验证阿里云无痕验证码
     *
     * @param param 前端获取的NVCVal值
     * @return 验证结果
     */
    @PostMapping("/verify")
    @Operation(summary = "验证阿里云无痕验证码", description = "验证前端传递的阿里云无痕验证码数据")
    public ApiResult<Integer> verifyCaptcha(@RequestBody VerifyParam param) {
        int result = captchaService.verifyNvcCaptcha(param.getData());
        return ApiResult.success(result);
    }
}
