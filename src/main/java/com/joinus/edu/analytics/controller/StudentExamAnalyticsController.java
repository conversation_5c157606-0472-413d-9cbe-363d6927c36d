package com.joinus.edu.analytics.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.edu.analytics.model.dto.QuestionTypeStats;
import com.joinus.edu.analytics.model.dto.StudentAnalyticsResult;
import com.joinus.edu.analytics.model.entity.ExamAnalyzeResult;
import com.joinus.edu.analytics.model.enums.QuestionAnalysisFilterEnum;
import com.joinus.edu.analytics.model.global.ApiResult;
import com.joinus.edu.analytics.model.param.KnowledgePointAnalyticeParam;
import com.joinus.edu.analytics.model.param.RecentExamAnalyticsParam;
import com.joinus.edu.analytics.model.param.StudentInfoParam;
import com.joinus.edu.analytics.model.result.ClassKnowledgePointAnalyticsResult;
import com.joinus.edu.analytics.model.result.RecentExamAnalyticsResult;
import com.joinus.edu.analytics.model.result.StudentKnowledgePointAnalyticsDto;
import com.joinus.edu.analytics.service.StudentExamAnalyticsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * @Description: 学生考情分析详情
 * @Author:  anpy
 * @date:  2025/4/17 09:50
 */
@RestController
@RequestMapping("/api/student-exam-analytics")
@Tag(name = "学生考情分析详情", description = "学生考情分析详情")
@RequiredArgsConstructor
public class StudentExamAnalyticsController {

    private final StudentExamAnalyticsService studentExamAnalyticsService;

    @Operation(summary = "最近考情分析记录", description = "最近考情分析记录")
    @GetMapping("/recent-exam-analytics")
    public ApiResult<IPage<RecentExamAnalyticsResult>> getRecentExamAnalytics(RecentExamAnalyticsParam param) {
        IPage<RecentExamAnalyticsResult> result = studentExamAnalyticsService.getRecentExamAnalytics(param);
        return ApiResult.success(result);
    }

    @Operation(summary = "基础数据", description = "基础数据")
    @GetMapping("/student-info")
    public ApiResult<StudentAnalyticsResult> studentInfo(StudentInfoParam param) {
        return ApiResult.success(studentExamAnalyticsService.studentInfo(param));
    }

    @Operation(summary = "答题情况概览", description = "答题情况概览")
    @GetMapping("/answer-overview")
    public ApiResult<List<QuestionTypeStats>> answerOverview(Long analyzeId) {
        return ApiResult.success(studentExamAnalyticsService.answerOverview(analyzeId));
    }

    @Operation(summary = "知识点详细分析", description = "获取指定考试中知识点的详细分析数据")
    @GetMapping("/knowledge-point-analysis")
    public ApiResult<IPage<StudentKnowledgePointAnalyticsDto>> getKnowledgePointAnalysis(KnowledgePointAnalyticeParam param) {
        return ApiResult.success(studentExamAnalyticsService.getKnowledgePointAnalysis(param));
    }
}
