package com.joinus.edu.analytics.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.joinus.edu.analytics.model.dto.ClassQuestionStatisticsDto;
import com.joinus.edu.analytics.model.entity.ClassStudentExamResultEntity;
import com.joinus.edu.analytics.model.enums.QuestionAnalysisFilterEnum;
import com.joinus.edu.analytics.model.global.ApiResult;
import com.joinus.edu.analytics.model.param.KnowledgePointAnalyticeParam;
import com.joinus.edu.analytics.model.param.QuestionAnalysisParam;
import com.joinus.edu.analytics.model.param.StudentAnalyticsParam;
import com.joinus.edu.analytics.model.result.ClassKnowledgePointAnalyticsResult;
import com.joinus.edu.analytics.model.result.ExamDetailsResult;
import com.joinus.edu.analytics.model.result.TopWrongQuestionResult;
import com.joinus.edu.analytics.service.ClassExamAnalyticsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * @Description: 班级考试分析接口
 * @Author:  anpy
 * @date:  2025/4/16 19:43
 */
@RestController
@RequestMapping("/class-exam-analytics")
@Tag(name = "班级考试分析接口", description = "提供班级考试数据分析相关功能")
@RequiredArgsConstructor
public class ClassExamAnalyticsController {

    @Resource
    private ClassExamAnalyticsService classExamAnalyticsService;

    @Operation(summary = "试卷详情", description = "获取试卷详细信息，包括试卷名称、参与人数、班级、生成报告人数")
    @GetMapping("/exam-details")
    public ApiResult<ExamDetailsResult> getExamDetails(@RequestParam UUID examId, @RequestParam Long classId) {
        return ApiResult.success(classExamAnalyticsService.getExamDetails(examId, classId));
    }

    @Operation(summary = "高频错题TOP5", description = "获取指定考试中错误率最高的前5道题目")
    @GetMapping("/top-wrong-questions")
    public ApiResult<List<TopWrongQuestionResult>> getTopWrongQuestions(
            @RequestParam UUID examId,
            @RequestParam Long classId) {
        return ApiResult.success(classExamAnalyticsService.getTopWrongQuestions(examId, classId));
    }

    @Operation(summary = "详细题目分析", description = "根据所有题目或错题类型查询题目分析数据，包含题型、知识点、错误人数、题目正确率、年级平均正确率等信息")
    @GetMapping("/question-analysis")
    public ApiResult<IPage<ClassQuestionStatisticsDto>> getQuestionAnalysis(QuestionAnalysisParam param) {
        return ApiResult.success(classExamAnalyticsService.getQuestionAnalysis(param));
    }

    @Operation(summary = "知识点详细分析", description = "获取指定考试中知识点的详细分析数据")
    @GetMapping("/knowledge-point-analysis")
    public ApiResult<IPage<ClassKnowledgePointAnalyticsResult>> getKnowledgePointAnalysis(KnowledgePointAnalyticeParam param) {
        return ApiResult.success(classExamAnalyticsService.getKnowledgePointAnalysis(param));
    }

    @Operation(summary = "学生考情分析报告", description = "分页获取该班级所有学生的考试分析报告，按报告生成时间倒序排列，未参与考试的学生状态为未参与")
    @GetMapping("/student-exam-report")
    public ApiResult<IPage<ClassStudentExamResultEntity>> getStudentExamReport(StudentAnalyticsParam param) {
        IPage<ClassStudentExamResultEntity> result = classExamAnalyticsService.getStudentExamReport(param);
        return ApiResult.success(result);
    }
}
