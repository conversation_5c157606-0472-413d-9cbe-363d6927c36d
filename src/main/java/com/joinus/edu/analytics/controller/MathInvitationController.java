package com.joinus.edu.analytics.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.joinus.edu.analytics.model.global.ApiResult;
import com.joinus.edu.analytics.model.param.MathInvitationParam;
import com.joinus.edu.analytics.model.result.MathInvitationPo;
import com.joinus.edu.analytics.service.MathInvitationService;
import com.joinus.edu.analytics.utils.BasicApiLocalHttpUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @Description: 邀请好友统计后台管理
 */
@RestController
@RequestMapping("/api/math-invitation")
@Tag(name = "邀请好友统计后台管理", description = "邀请好友统计后台管理")
@Slf4j
@RequiredArgsConstructor
public class MathInvitationController {

    @Value("${basic-api.domain.url:https://basic-api.uat.ijiaxiao.net}")
    private String basicApiDomainUrl;

    @Autowired
    private MathInvitationService mathInvitationService;

    @Operation(summary = "获取学校信息", description = "获取学校信息")
    @GetMapping("/school/list")
    public ApiResult getSchoolList(String schoolName, String operator, String telNum) {
        String url = basicApiDomainUrl + "/external/school?schoolName=" + schoolName;
        try {
            String responseBody = BasicApiLocalHttpUtil.get(url, operator, telNum);
            if (StrUtil.isNotBlank(responseBody)) {
                JSONObject jsonResponse = JSONUtil.parseObj(responseBody);
                JSONArray data = jsonResponse.getJSONArray("data");
                return ApiResult.success(data);
            }
        }catch (Exception e){
            return ApiResult.failed("调用基础服务获取学校信息失败");
        }
        return ApiResult.success();
    }

    @Operation(summary = "获取年级信息", description = "获取年级信息")
    @GetMapping("/grade/list")
    public ApiResult getGradeList(Long schoolId, String operator, String telNum) {
        String url = basicApiDomainUrl + "/external/grade/" + schoolId;
        try {
            String responseBody = BasicApiLocalHttpUtil.get(url, operator, telNum);
            if (StrUtil.isNotBlank(responseBody)) {
                JSONObject jsonResponse = JSONUtil.parseObj(responseBody);
                JSONArray data = jsonResponse.getJSONArray("data");
                return ApiResult.success(data);
            }
        }catch (Exception e){
            return ApiResult.failed("调用基础服务获取年级信息失败");
        }
        return ApiResult.success();
    }

    @Operation(summary = "获取班级信息", description = "获取班级信息")
    @GetMapping("/class/list")
    public ApiResult getClassList(Long gradeId, String operator, String telNum) {
        String url = basicApiDomainUrl + "/external/class/" + gradeId;
        try {
            String responseBody = BasicApiLocalHttpUtil.get(url, operator, telNum);
            if (StrUtil.isNotBlank(responseBody)) {
                JSONObject jsonResponse = JSONUtil.parseObj(responseBody);
                JSONArray data = jsonResponse.getJSONArray("data");
                return ApiResult.success(data);
            }
        }catch (Exception e){
            return ApiResult.failed("调用基础服务获取班级信息失败");
        }
        return ApiResult.success();
    }


    @Operation(summary = "邀请好友统计后台管理", description = "邀请好友统计后台管理")
    @PostMapping("/page")
    public ApiResult<IPage<MathInvitationPo>> getRecentExamAnalytics(@RequestBody MathInvitationParam param) {
        IPage<MathInvitationPo> result = mathInvitationService.getMathInvitationResultList(param);
        return ApiResult.success(result);
    }

}
