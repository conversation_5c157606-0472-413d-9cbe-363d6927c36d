package com.joinus.edu.analytics.config;

import com.joinus.edu.analytics.interceptor.TokenInterceptor;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web MVC 配置类
 */
@Slf4j
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Resource
    private TokenInterceptor tokenInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        log.info("注册Token拦截器");
        // 注册 Token 拦截器，拦截所有请求
//        registry.addInterceptor(tokenInterceptor)
//                // 拦截所有请求，不仅仅是特定前缀
//                .addPathPatterns("/**")
//                // 排除登录相关接口
//                .excludePathPatterns(
//                        "/login/**",
//                        "/swagger-ui/**",
//                        "/swagger-resources/**",
//                        "/v3/api-docs/**",
//                        "/error",
//                        "/actuator/**",
//                        "/api/qyl-manager/**"
//                );
    }
}
