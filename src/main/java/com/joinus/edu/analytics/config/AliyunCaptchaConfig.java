package com.joinus.edu.analytics.config;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 阿里云验证码配置类
 */
@Configuration
@Slf4j
public class AliyunCaptchaConfig {

    @Value("${aliyun.captcha.regionId:cn-hangzhou}")
    private String regionId;

    @Value("${aliyun.captcha.accessKeyId}")
    private String accessKeyId;

    @Value("${aliyun.captcha.accessKeySecret}")
    private String accessKeySecret;

    /**
     * 初始化阿里云验证码客户端
     *
     * @return IAcsClient 阿里云验证码客户端
     */
    @Bean
    public IAcsClient acsClient() {
        IClientProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
        DefaultProfile.addEndpoint(regionId, "afs", "afs.aliyuncs.com");
        log.info("阿里云验证码客户端初始化");
        return new DefaultAcsClient(profile);
    }
}
