package com.joinus.edu.analytics.config;

import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * HTTP客户端连接池配置
 *
 * <AUTHOR>
 * @date 2025/4/28
 */
@Slf4j
@Configuration
public class HttpClientConfig {

    /**
     * 最大空闲连接数
     */
    private static final int MAX_IDLE_CONNECTIONS = 50;

    /**
     * 连接保持空闲时间（分钟）
     */
    private static final long KEEP_ALIVE_DURATION = 5;

    /**
     * 连接超时时间（秒）
     */
    private static final long CONNECT_TIMEOUT = 30;

    /**
     * 读取超时时间（秒）
     */
    private static final long READ_TIMEOUT = 30;

    /**
     * 写入超时时间（秒）
     */
    private static final long WRITE_TIMEOUT = 30;

    /**
     * 配置OkHttp客户端
     *
     * @return OkHttpClient实例
     */
    @Bean
    public OkHttpClient okHttpClient() {
        return new OkHttpClient.Builder()
                .connectionPool(new ConnectionPool(MAX_IDLE_CONNECTIONS, KEEP_ALIVE_DURATION, TimeUnit.MINUTES))
                .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
                .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
                .writeTimeout(WRITE_TIMEOUT, TimeUnit.SECONDS)
                .retryOnConnectionFailure(true)
                .build();
    }
}
