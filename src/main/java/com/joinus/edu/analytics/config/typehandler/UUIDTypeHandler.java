package com.joinus.edu.analytics.config.typehandler;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.UUID;

/**
 * UUIDu7c7bu578bu5904u7406u5668uff0cu7528u4e8eu5904u7406Java UUIDu7c7bu578bu4e0ePostgreSQL UUIDu7c7bu578bu7684u8f6cu6362
 */
@MappedTypes(UUID.class)
public class UUIDTypeHandler extends BaseTypeHandler<UUID> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, UUID parameter, JdbcType jdbcType) throws SQLException {
        ps.setObject(i, parameter);
    }

    @Override
    public UUID getNullableResult(ResultSet rs, String columnName) throws SQLException {
        Object uuid = rs.getObject(columnName);
        if (uuid == null) {
            return null;
        }
        return convertToUUID(uuid);
    }

    @Override
    public UUID getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        Object uuid = rs.getObject(columnIndex);
        if (uuid == null) {
            return null;
        }
        return convertToUUID(uuid);
    }

    @Override
    public UUID getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        Object uuid = cs.getObject(columnIndex);
        if (uuid == null) {
            return null;
        }
        return convertToUUID(uuid);
    }
    
    private UUID convertToUUID(Object uuid) {
        if (uuid instanceof UUID) {
            return (UUID) uuid;
        } else if (uuid instanceof String) {
            return UUID.fromString((String) uuid);
        } else {
            return UUID.fromString(uuid.toString());
        }
    }
}
