package com.joinus.edu.analytics.service.impl;

import com.aliyuncs.IAcsClient;
import com.aliyuncs.afs.model.v20180112.AnalyzeNvcRequest;
import com.aliyuncs.afs.model.v20180112.AnalyzeNvcResponse;
import com.aliyuncs.exceptions.ClientException;
import com.joinus.edu.analytics.model.global.CommonResponse;
import com.joinus.edu.analytics.service.CaptchaService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 验证码服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CaptchaServiceImpl implements CaptchaService {

    private final IAcsClient acsClient;

    /**
     * 验证阿里云无痕验证码
     *
     * @param data 前端获取的NVCVal值
     * @return 验证结果，true表示验证通过，false表示验证失败
     */
    @Override
    public int verifyNvcCaptcha(String data) {
        AnalyzeNvcRequest request = new AnalyzeNvcRequest();
        request.setData(data);
        // 设置验证结果与前端操作的映射关系
        // 200：直接通过，400：需要滑动验证，800：直接拦截
        request.setScoreJsonStr("{\"200\":\"PASS\",\"400\":\"NC\",\"800\":\"BLOCK\"}");
        
        try {
            AnalyzeNvcResponse response = acsClient.getAcsResponse(request);
            // 将 getBizCode() 的返回值转换为整数类型
            int bizCode = Integer.parseInt(String.valueOf(response.getBizCode()));
            log.info("阿里云无痕验证结果：{}, {}", bizCode, getVerifyResult(bizCode));
            // 100表示验签通过，200表示直接通过
            return bizCode;
        } catch (ClientException e) {
            log.error("阿里云无痕验证异常：", e);
            return 200;
        }
    }

    /**
     * 获取验证结果代码对应的处理方式
     *
     * @param bizCode 业务代码
     * @return 处理方式描述
     */
    @Override
    public String getVerifyResult(int bizCode) {
        switch (bizCode) {
            case 100:
                return "验签通过";
            case 200:
                return "直接通过";
            case 400:
                return "需要滑动验证";
            case 800:
                return "直接拦截";
            case 900:
                return "验签失败";
            default:
                return "未知状态";
        }
    }
}
