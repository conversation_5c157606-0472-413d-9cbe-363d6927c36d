package com.joinus.edu.analytics.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.joinus.edu.analytics.exception.BaseException;
import com.joinus.edu.analytics.model.global.CommonResponse;
import com.joinus.edu.analytics.model.global.Constants;
import com.joinus.edu.analytics.model.param.AccountLoginParam;
import com.joinus.edu.analytics.model.result.AccountLoginResult;
import com.joinus.edu.analytics.model.result.LoginResult;
import com.joinus.edu.analytics.service.CaptchaService;
import com.joinus.edu.analytics.service.LoginService;
import com.joinus.edu.analytics.service.TokenService;
import com.joinus.edu.analytics.utils.OkHttpUtil;
import com.joinus.edu.analytics.utils.RedisUtils;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.OutputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * @Description: 登录业务实现类
 * @Author: anpy
 * @date: 2025/4/7 10:13
 */
@Slf4j
@Service
public class LoginServiceImpl implements LoginService {

    @Resource
    private TokenService tokenService;
    
    @Resource
    private CaptchaService captchaService;
    
    @Resource
    private OkHttpUtil okHttpUtil;

    /**
     * 登录失败重试时间
     */
    @Value("${error.password.wait.time:600}")
    private int errorPasswordWaitTime;

    @Value("${basic-api-url:http://*************:9091}")
    private String basicApiUrl;

    /**
     * 账号密码登录
     *
     * @param param   account: 账号
     *                password: 密码
     */
    @Override
    public LoginResult accountLogin(AccountLoginParam param) {
        
        // 检查登录失败次数
        checkLoginFailures(param.getAccount());

        // 验证账号密码
        AccountLoginResult.DataDTO user = authenticateUser(param);

        // 清除登录失败记录
        RedisUtils.delete(Constants.LOGIN_ERROR_COUNT + param.getAccount());

        // 生成 token
        String token = tokenService.generateToken(user);

        // 返回登录结果
        return LoginResult.builder()
                .teacherId(user.getId())
                .teacherName(user.getTeacherName())
                .token(token)
                .build();
    }

    /**
     * 检查登录失败次数
     */
    private void checkLoginFailures(String account) {
        String loginErrorCount = RedisUtils.get(Constants.LOGIN_ERROR_COUNT + account);
        int loginErrorCountInt = NumberUtils.toInt(loginErrorCount, 0);
        if (loginErrorCountInt >= Constants.loginErrorCount) {
            throw new BaseException("您的帐户已被锁定,请" + errorPasswordWaitTime / 60 + "分钟后重试");
        }
    }

    /**
     * 验证账号密码
     */
    private AccountLoginResult.DataDTO authenticateUser(AccountLoginParam param) {
        Map<String, Object> params = new HashMap<>(2);
        params.put("account", param.getAccount());
        params.put("password", param.getPassword());

        String response = okHttpUtil.get(basicApiUrl + Constants.HTTP_GET_TEACHER_INFO_URL, params);
        AccountLoginResult accountLoginResult = JSONUtil.toBean(response, AccountLoginResult.class);

        if (accountLoginResult.getCode() != CommonResponse.SUCCESS.getCode()) {
            // 记录登录失败
            String loginErrorCount = RedisUtils.get(Constants.LOGIN_ERROR_COUNT + param.getAccount());
            int loginErrorCountInt = NumberUtils.toInt(loginErrorCount, 0);
            RedisUtils.set(Constants.LOGIN_ERROR_COUNT + param.getAccount(),
                    String.valueOf(loginErrorCountInt + 1),
                    errorPasswordWaitTime);

            throw new BaseException("账号密码错误");
        }

        return accountLoginResult.getData();
    }

    @Override
    public void getCaptcha(HttpServletRequest request, HttpServletResponse response) throws IOException {
        // 设置响应的类型格式为图片格式
        response.setContentType("image/jpeg");
        // 禁止图像缓存
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Cache-Control", "no-cache");
        response.setDateHeader("Expires", 0);

        // 创建验证码对象
        int width = 100; // 验证码图片宽度
        int height = 40; // 验证码图片高度
        int codeCount = 4; // 验证码字符数
        int lineCount = 20; // 干扰线数量

        // 创建图像缓冲区
        BufferedImage buffImg = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g = buffImg.createGraphics();

        // 创建一个随机数生成器
        Random random = new Random();

        // 设置图像背景
        g.setColor(Color.WHITE);
        g.fillRect(0, 0, width, height);

        // 创建字体
        Font font = new Font("Fixedsys", Font.BOLD, 24);
        g.setFont(font);

        // 画边框
        g.setColor(Color.BLACK);
        g.drawRect(0, 0, width - 1, height - 1);

        // 随机产生干扰线
        g.setColor(Color.BLACK);
        for (int i = 0; i < lineCount; i++) {
            int x = random.nextInt(width);
            int y = random.nextInt(height);
            int xl = random.nextInt(width);
            int yl = random.nextInt(height);
            g.drawLine(x, y, x + xl, y + yl);
        }

        // 生成随机验证码
        StringBuilder randomCode = new StringBuilder();
        // 验证码字符集
        String chars = "ABCDEFGHJKLMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789";

        // 随机产生验证码字符
        for (int i = 0; i < codeCount; i++) {
            // 得到随机字符
            char c = chars.charAt(random.nextInt(chars.length()));
            // 将随机字符加入到验证码中
            randomCode.append(c);
            // 用随机颜色画验证码字符
            g.setColor(new Color(random.nextInt(100), random.nextInt(100), random.nextInt(100)));
            g.drawString(String.valueOf(c), (i * 20) + 15, 25);
        }

        // 将验证码存入session
        request.getSession().setAttribute("randomCode", randomCode.toString());

        // 释放图形上下文
        g.dispose();

        // 输出图像到页面
        OutputStream sos = response.getOutputStream();
        ImageIO.write(buffImg, "jpeg", sos);
        sos.close();
    }
}
