package com.joinus.edu.analytics.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.edu.analytics.model.dto.PersonalExamDto;
import com.joinus.edu.analytics.model.entity.PersonalExam;
import com.joinus.edu.analytics.model.param.StudentInfoParam;
public interface PersonalExamService extends IService<PersonalExam> {

    IPage<PersonalExamDto> getStudentExamPage(StudentInfoParam param);
}
