package com.joinus.edu.analytics.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.joinus.edu.analytics.model.dto.MathCatalogNodeDto;
import com.joinus.edu.analytics.model.dto.MathKnowledgePointDto;
import com.joinus.edu.analytics.model.entity.MathStudentStudyPlan;
import com.joinus.edu.analytics.model.enums.BookVolumeEnum;
import com.joinus.edu.analytics.model.enums.GradeEnum;
import com.joinus.edu.analytics.model.enums.PublisherEnum;
import com.joinus.edu.analytics.model.enums.SchoolStageEnum;
import com.joinus.edu.analytics.model.param.CreateStudentPlanParam;
import com.joinus.edu.analytics.model.param.PreviewStudentPlanParam;
import com.joinus.edu.analytics.model.po.MathKnowledgePointPo;
import com.joinus.edu.analytics.model.po.MathPersonalExamKnowledgePointPo;
import com.joinus.edu.analytics.model.result.MathBookNodeResult;
import com.joinus.edu.analytics.model.result.MathCatalogNodeResult;
import com.joinus.edu.analytics.model.result.MathKnowledgePointResult;
import com.joinus.edu.analytics.model.result.MathStudentPlanResult;
import com.joinus.edu.analytics.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
public class StudyPlanServiceImpl implements StudyPlanService {

    @Autowired
    private EduKnowledgeHubService eduKnowledgeHubService;
    @Autowired
    private StudentExamAnalyticsService studentExamAnalyticsService;
    @Autowired
    private MathKnowledgePointService mathKnowledgePointService;
    @Autowired
    private MathStudentStudyPlanService mathStudentStudyPlanService;

    @Override
    public List<MathCatalogNodeResult> listKnowlwdgePointsTree(PublisherEnum publisher, Integer grade, BookVolumeEnum bookVolume) {

        //根据书查询所有节点
        Integer semester = bookVolume.getVolumeNum();
        List<MathCatalogNodeDto> catalogNodeDtos = eduKnowledgeHubService.listCatalogNodes(grade, publisher, semester);
        //根据catalogNodeDtos取出所有的nodeId
        Set<UUID> nodeIds = extractAllNodeIds(catalogNodeDtos);
        //查询各节点下的知识点
        List<MathKnowledgePointDto> kpDtos = eduKnowledgeHubService.listKnowledgePointsByNodeIds(nodeIds);

        // 将知识点按sectionId分组
        Map<UUID, List<MathKnowledgePointDto>> kpMap = kpDtos.stream()
                .sorted(Comparator.comparing(MathKnowledgePointDto::getSortNo))
                .collect(Collectors.groupingBy(
                        MathKnowledgePointDto::getSectionId,
                        Collectors.toList()
                ));

        // 将知识点装配到对应的节点中
        List<MathCatalogNodeResult> results = assembleKnowledgePoints(catalogNodeDtos, kpMap, grade, null);
        //TODO 回显学习计划中已经勾选的知识点
        return results;
    }

    @Override
    public List<MathBookNodeResult> listKnowlwdgePointsTreeByExamAnalyzeResultIds(List<Long> examAnalyzeResultIds) {
        //查询考试分析报告中的薄弱知识点
        List<MathPersonalExamKnowledgePointPo> weakKnowledgePoints = studentExamAnalyticsService.listWeakKnowledgePointsByExamAnalyzeResultIds(examAnalyzeResultIds);
        if (CollUtil.isEmpty(weakKnowledgePoints)) {
            throw new RuntimeException("没有找到对应的薄弱知识点");
        }

        //根据知识点反查询知识点树形结构
        List<UUID> weakKpIds = weakKnowledgePoints.stream().map(MathPersonalExamKnowledgePointPo::getKnowledgePointId).toList();

        List<MathCatalogNodeDto> catalogNodeDtos = eduKnowledgeHubService.listCatalogNodesByKpIds(weakKpIds);

        //处理叶子节点下的知识点

        List<MathKnowledgePointPo> kpPos = mathKnowledgePointService.listByIds(weakKpIds);
        Map<UUID, List<MathKnowledgePointPo>> kpMap = kpPos.stream().collect(Collectors.groupingBy(MathKnowledgePointPo::getNodeId));
        buildLeafNodeKnowledgePints(catalogNodeDtos, kpMap);

        List<MathBookNodeResult> results = new ArrayList<>();

        Map<String, List<MathCatalogNodeDto>> collect = catalogNodeDtos.stream().collect(Collectors.groupingBy(node ->
                node.getPublisher() + "-" + node.getGrade() + "-" + node.getSemester()));

        collect.forEach((key, value) -> {
            List<String> split = StrUtil.split(key, "-");
            MathBookNodeResult result = MathBookNodeResult.builder()
                    .grade(Integer.parseInt(split.get(1)))
                    .publisher(PublisherEnum.valueOf(split.get(0)))
                    .bookVolume(BookVolumeEnum.fromVolumeNum(Integer.parseInt(split.get(2))))
                    .build();
            result.setSubNodes(value.stream().map(MathCatalogNodeResult::ofMathCatalogNodeDto).toList());
            results.add(result);
        });



        return results.stream().sorted(Comparator.comparing((MathBookNodeResult result) -> result.getPublisher().getSortNo())
                .thenComparing(MathBookNodeResult::getGrade)
                .thenComparing(MathBookNodeResult::getBookVolume))
                .collect(Collectors.toList());
    }

    @Override
    public List<JSONObject> listBooksTree() {
        List<PublisherEnum> publishers = Arrays.asList(PublisherEnum.values());
        List<JSONObject> results = new ArrayList<>();
        publishers.stream().forEach(publisher -> {
            List<SchoolStageEnum> schoolStages = Arrays.asList(SchoolStageEnum.values()).stream().filter(item -> item.getPublishers().contains(publisher)).toList();
            List<GradeEnum> grades = new ArrayList<>();
            schoolStages.stream().forEach(schoolStage -> {
                grades.addAll(Arrays.asList(GradeEnum.values()).stream().filter(item -> item.getSchoolStage().equals(schoolStage)).toList());
            });
            List<JSONObject> gradeList = grades.stream().map(grade -> {
                return new JSONObject()
                        .set("grade", grade.getValue())
                        .set("gradeDesc", grade.getDesc())
                        .set("bookVolumes", grade.getBookVolumes().stream().map(bookVolume -> {
                            return new JSONObject().set("bookVolume", bookVolume.name())
                                    .set("bookVolumeDesc", bookVolume.getDesc());
                        }).toList());
            }).toList();
            results.add(new JSONObject()
                    .set("publisher", publisher)
                    .set("publisherDesc", publisher.getDescription())
                    .set("grades", gradeList));
        });
        return results;
    }

    @Override
    public List<MathStudentPlanResult> previewStudentPlan(PreviewStudentPlanParam param) {
        List<MathCatalogNodeDto> catalogNodeDtos = eduKnowledgeHubService.listCatalogNodesByKpIds(param.getKnowledgePointIds());
        //处理叶子节点下的知识点

        List<MathKnowledgePointPo> kpPos = mathKnowledgePointService.listByIds(param.getKnowledgePointIds());
        Map<UUID, List<MathKnowledgePointPo>> kpMap = kpPos.stream().collect(Collectors.groupingBy(MathKnowledgePointPo::getNodeId));
        buildLeafNodeKnowledgePints(catalogNodeDtos, kpMap);

        List<MathBookNodeResult> results = new ArrayList<>();

        Map<String, List<MathCatalogNodeDto>> nodeMap = catalogNodeDtos.stream().collect(Collectors.groupingBy(node ->
                node.getPublisher() + "-" + node.getGrade() + "-" + node.getSemester()));

        nodeMap.forEach((key, value) -> {
            List<String> split = StrUtil.split(key, "-");
            MathBookNodeResult result = MathBookNodeResult.builder()
                    .grade(Integer.parseInt(split.get(1)))
                    .publisher(PublisherEnum.valueOf(split.get(0)))
                    .bookVolume(BookVolumeEnum.fromVolumeNum(Integer.parseInt(split.get(2))))
                    .build();
            result.setSubNodes(value.stream().map(MathCatalogNodeResult::ofMathCatalogNodeDto).toList());
            results.add(result);
        });


        List<MathBookNodeResult> collet = results.stream().sorted(Comparator.comparing((MathBookNodeResult result) -> result.getPublisher().getSortNo())
                        .thenComparing(MathBookNodeResult::getGrade)
                        .thenComparing(MathBookNodeResult::getBookVolume))
                .collect(Collectors.toList());

        List<MathStudentPlanResult> plans = buildKnowledgePointPlan(collet);
        // 处理weekNo和sortNo，每4个知识点算一周
        assignWeekAndSortNumbers(plans, param.getWeekKnowledgePointCount());
        //TODO 补充知识掌握程度
        return plans;
    }

    @Override
    public List<MathStudentPlanResult> createStudentPlan(CreateStudentPlanParam param) {
        List<MathStudentPlanResult> plans = mathStudentStudyPlanService.listByStudentId(param.getStudentId());
        if (CollUtil.isNotEmpty(plans)) {
            //删除所有旧数据
            mathStudentStudyPlanService.removeByStudentId(param.getStudentId());
        }
        List<MathStudentStudyPlan> newPlans = (List<MathStudentStudyPlan>) param.getPlans().stream()
                .map(plan -> {
                    return MathStudentStudyPlan.builder()
                            .studentId(param.getStudentId())
                            .weekNo(plan.getWeekNo())
                            .sortNo(plan.getSortNo())
                            .knowledgePointId(plan.getKnowledgePointId())
                            .build();
                }).toList();
        mathStudentStudyPlanService.saveBatch(newPlans);
        return mathStudentStudyPlanService.listByStudentId(param.getStudentId());
    }

    private List<MathStudentPlanResult> buildKnowledgePointPlan(List<MathBookNodeResult> nodes) {
        // 方案1：使用Stream API的flatMap方式（推荐）
        List<MathStudentPlanResult> kpResults = nodes.stream()
                .filter(node -> CollUtil.isNotEmpty(node.getSubNodes()))
                .flatMap(node -> flattenKnowledgePoints(node.getSubNodes()).stream())
                .collect(Collectors.toList());
        return kpResults;
    }

    /**
     * 递归平铺所有叶子节点的知识点
     */
    private List<MathStudentPlanResult> flattenKnowledgePoints(List<MathCatalogNodeResult> nodes) {
        return nodes.stream()
                .flatMap(node -> {
                    if (CollUtil.isNotEmpty(node.getSubNodes())) {
                        // 非叶子节点，递归处理子节点
                        return flattenKnowledgePoints(node.getSubNodes()).stream();
                    } else {
                        // 叶子节点，提取知识点
                        return Optional.ofNullable(node.getKnowledgePoints())
                                .orElse(Collections.emptyList())
                                .stream()
                                .map(kp -> MathStudentPlanResult.builder()
                                        .gradeDesc(GradeEnum.ofValue(node.getGrade()).getDesc())
                                        .knowledgePointId(kp.getId())
                                        .knowledgePointName(kp.getName())
                                        .build());
                    }
                })
                .collect(Collectors.toList());
    }

    /**
     * 分配周数和排序号
     */
    private List<MathStudentPlanResult> assignWeekAndSortNumbers(List<MathStudentPlanResult> results, Integer weekKnowledgePointCount) {
        if (null == weekKnowledgePointCount || weekKnowledgePointCount <= 0) {
            throw new IllegalArgumentException("weekKnowledgePointCount must be greater than 0");
        }
        for (int i = 0; i < results.size(); i++) {
            MathStudentPlanResult result = results.get(i);
            int weekNo = (i / weekKnowledgePointCount) + 1;  // 每4个知识点算一周，从第1周开始
            int sortNo = (i % weekKnowledgePointCount) + 1;  // 周内排序，从1开始

            result.setWeekNo(weekNo);
            result.setSortNo(sortNo);
        }
        return results;
    }

    // ========== 其他可选方案 ==========

    /**
     * 方案2：使用AtomicInteger计数器的方式
     * 优点：在平铺过程中直接分配周数和排序号，避免二次遍历
     */
    private List<MathStudentPlanResult> buildKnowledgePointPlanWithCounter(List<MathBookNodeResult> nodes) {
        List<MathStudentPlanResult> results = new ArrayList<>();
        AtomicInteger counter = new AtomicInteger(0);

        nodes.stream()
                .filter(node -> CollUtil.isNotEmpty(node.getSubNodes()))
                .forEach(node -> flattenWithCounter(node.getSubNodes(), results, counter));

        return results;
    }

    private void flattenWithCounter(List<MathCatalogNodeResult> nodes,
                                   List<MathStudentPlanResult> results,
                                   AtomicInteger counter) {
        nodes.forEach(node -> {
            if (CollUtil.isNotEmpty(node.getSubNodes())) {
                flattenWithCounter(node.getSubNodes(), results, counter);
            } else if (CollUtil.isNotEmpty(node.getKnowledgePoints())) {
                node.getKnowledgePoints().forEach(kp -> {
                    int index = counter.getAndIncrement();
                    results.add(MathStudentPlanResult.builder()
                            .gradeDesc(GradeEnum.ofValue(node.getGrade()).getDesc())
                            .knowledgePointId(kp.getId())
                            .knowledgePointName(kp.getName())
                            .weekNo((index / 4) + 1)
                            .sortNo((index % 4) + 1)
                            .build());
                });
            }
        });
    }

    /**
     * 方案3：使用访问者模式
     * 优点：更好的扩展性，可以轻松添加其他类型的处理逻辑
     */
    private List<MathStudentPlanResult> buildKnowledgePointPlanWithVisitor(List<MathBookNodeResult> nodes) {
        KnowledgePointCollector collector = new KnowledgePointCollector();
        nodes.forEach(node -> visitNode(node, collector));
        return collector.getResultsWithWeekAndSort();
    }

    private void visitNode(MathBookNodeResult bookNode, KnowledgePointCollector collector) {
        if (CollUtil.isNotEmpty(bookNode.getSubNodes())) {
            bookNode.getSubNodes().forEach(node -> visitCatalogNode(node, collector));
        }
    }

    private void visitCatalogNode(MathCatalogNodeResult node, KnowledgePointCollector collector) {
        if (CollUtil.isNotEmpty(node.getSubNodes())) {
            node.getSubNodes().forEach(subNode -> visitCatalogNode(subNode, collector));
        } else if (CollUtil.isNotEmpty(node.getKnowledgePoints())) {
            collector.collectKnowledgePoints(node);
        }
    }

    /**
     * 知识点收集器
     */
    private static class KnowledgePointCollector {
        private final List<MathStudentPlanResult> results = new ArrayList<>();

        public void collectKnowledgePoints(MathCatalogNodeResult node) {
            node.getKnowledgePoints().forEach(kp -> {
                results.add(MathStudentPlanResult.builder()
                        .gradeDesc(GradeEnum.ofValue(node.getGrade()).getDesc())
                        .knowledgePointId(kp.getId())
                        .knowledgePointName(kp.getName())
                        .build());
            });
        }

        public List<MathStudentPlanResult> getResultsWithWeekAndSort() {
            for (int i = 0; i < results.size(); i++) {
                MathStudentPlanResult result = results.get(i);
                result.setWeekNo((i / 4) + 1);
                result.setSortNo((i % 4) + 1);
            }
            return results;
        }
    }

    private void buildLeafNodeKnowledgePints(List<MathCatalogNodeDto> nodeDtos, Map<UUID, List<MathKnowledgePointPo>> kpMap) {
        for (MathCatalogNodeDto nodeDto : nodeDtos) {
            List<MathKnowledgePointPo> kps = kpMap.getOrDefault(nodeDto.getId(), Collections.emptyList());
            nodeDto.setKnowledgePoints(kps);
            if (CollUtil.isNotEmpty(nodeDto.getSubNodes())) {
                buildLeafNodeKnowledgePints(nodeDto.getSubNodes(), kpMap);
            }
        }
    }

    private Set<UUID> extractAllNodeIds(List<MathCatalogNodeDto> catalogNodeDtos) {
        Set<UUID> nodeIds = new HashSet<>();
        if (catalogNodeDtos == null || catalogNodeDtos.isEmpty()) {
            return nodeIds;
        }

        for (MathCatalogNodeDto node : catalogNodeDtos) {
            // 添加当前节点的ID
            if (node.getId() != null) {
                nodeIds.add(node.getId());
            }

            // 递归处理子节点
            if (node.getSubNodes() != null && !node.getSubNodes().isEmpty()) {
                nodeIds.addAll(extractAllNodeIds(node.getSubNodes()));
            }
        }

        return nodeIds;
    }

    private List<MathCatalogNodeResult> assembleKnowledgePoints(List<MathCatalogNodeDto> nodes, Map<UUID, List<MathKnowledgePointDto>> kpMap, Integer grade, MathCatalogNodeDto parentNode) {
        if (nodes == null || nodes.isEmpty()) {
            return List.of();
        }

        List<MathCatalogNodeResult> results = new ArrayList<>();
        for (MathCatalogNodeDto node : nodes) {
            MathCatalogNodeResult result = MathCatalogNodeResult.ofMathCatalogNodeDto(node);

            // 获取当前节点的知识点
            List<MathKnowledgePointDto> knowledgePoints = kpMap.getOrDefault(node.getId(), List.of());
            if (CollUtil.isNotEmpty(knowledgePoints)) {
                result.setKnowledgePoints(knowledgePoints.stream()
                        .map(kp ->
                                MathKnowledgePointResult.builder()
                                        .id(kp.getId())
                                        .name(kp.getName())
                                        .sortNo(kp.getSortNo())
                                        .build())
                        .toList());
            }

            // 递归处理子节点
            if (CollUtil.isNotEmpty(node.getSubNodes())) {
                List<MathCatalogNodeResult> children = assembleKnowledgePoints(node.getSubNodes(), kpMap, grade, node);
                result.setSubNodes(children);
            } else {
                result.setSubNodes(List.of());
            }

            results.add(result);
        }

        return results;
    }
}
