package com.joinus.edu.analytics.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.joinus.edu.analytics.model.dto.QuestionTypeStats;
import com.joinus.edu.analytics.model.dto.StudentAnalyticsResult;
import com.joinus.edu.analytics.model.param.KnowledgePointAnalyticeParam;
import com.joinus.edu.analytics.model.param.RecentExamAnalyticsParam;
import com.joinus.edu.analytics.model.param.StudentInfoParam;
import com.joinus.edu.analytics.model.po.MathPersonalExamKnowledgePointPo;
import com.joinus.edu.analytics.model.result.RecentExamAnalyticsResult;
import com.joinus.edu.analytics.model.result.StudentKnowledgePointAnalyticsDto;

import java.util.List;

/**
 * 学生考情分析服务接口
 */
public interface StudentExamAnalyticsService {
    
    /**
     * 获取学生最近考情分析记录
     * @param param 查询参数，包含学生ID、页码、每页大小
     * @return 考情分析记录列表
     */
    IPage<RecentExamAnalyticsResult> getRecentExamAnalytics(RecentExamAnalyticsParam param);

    /**
     * 获取学生考情分析报告基本信息
     * @param param
     *  studentId 学生ID
     * @return 考情分析报告基本信息
     */
    StudentAnalyticsResult studentInfo(StudentInfoParam param);

    /**
     * 答题情况概览
     * @param analyzeId 考情分析报告id
     */
    List<QuestionTypeStats> answerOverview(Long analyzeId);

    /**
     * 知识点详细分析
     * @param param 分析参数，包含班级ID、考试ID、学生ID等
     * @return 知识点分析结果列表
     */
    IPage<StudentKnowledgePointAnalyticsDto> getKnowledgePointAnalysis(KnowledgePointAnalyticeParam param);

    /**
     * 根据考情分析报告ID列表获取薄弱知识点
     * @param examAnalyzeResultIds
     * @return
     */
    List<MathPersonalExamKnowledgePointPo> listWeakKnowledgePointsByExamAnalyzeResultIds(List<Long> examAnalyzeResultIds);
}
