package com.joinus.edu.analytics.service;

import com.joinus.edu.analytics.model.param.AccountLoginParam;
import com.joinus.edu.analytics.model.result.LoginResult;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;

/**
 * @Description: 登录业务接口
 * @Author:  anpy
 * @date:  2025/4/16 15:47
 */
public interface LoginService {

    /**
     * 账号密码登录
     *
     * @param param account: 账号
     *              password: 密码
     * @return 登录信息
     */
    LoginResult accountLogin(AccountLoginParam param);

    void getCaptcha(HttpServletRequest request, HttpServletResponse response) throws IOException;

}
