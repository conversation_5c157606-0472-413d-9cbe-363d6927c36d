package com.joinus.edu.analytics.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.edu.analytics.exception.BaseException;
import com.joinus.edu.analytics.mapper.*;
import com.joinus.edu.analytics.model.dto.QuestionTypeStats;
import com.joinus.edu.analytics.model.dto.StudentAnalyticsDto;
import com.joinus.edu.analytics.model.dto.StudentAnalyticsResult;
import com.joinus.edu.analytics.model.entity.*;
import com.joinus.edu.analytics.model.enums.QuestionTypeEnum;
import com.joinus.edu.analytics.model.enums.ResultEnum;
import com.joinus.edu.analytics.model.global.CommonResponse;
import com.joinus.edu.analytics.model.param.ExamAnalyzeResultParam;
import com.joinus.edu.analytics.model.param.KnowledgePointAnalyticeParam;
import com.joinus.edu.analytics.model.param.RecentExamAnalyticsParam;
import com.joinus.edu.analytics.model.param.StudentInfoParam;
import com.joinus.edu.analytics.model.po.MathPersonalExamKnowledgePointPo;
import com.joinus.edu.analytics.model.result.ExamAnalyzeResultResult;
import com.joinus.edu.analytics.model.result.RecentExamAnalyticsResult;
import com.joinus.edu.analytics.model.result.StudentKnowledgePointAnalyticsDto;
import com.joinus.edu.analytics.service.StudentExamAnalyticsService;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 学生考情分析服务实现类
 */
@Service
@RequiredArgsConstructor
public class StudentExamAnalyticsServiceImpl implements StudentExamAnalyticsService {

    @Resource
    private ExamAnalyzeResultMapper examAnalyzeResultMapper;
    @Resource
    private MathExamMapper mathExamMapper;
    @Resource
    private PersonalExamQuestionMapper personalExamQuestionMapper;
    @Resource
    private KnowledgePointMapper knowledgePointMapper;
    @Resource
    private ClassKnowledgePointStatisticsMapper classKnowledgePointStatisticsMapper;
    @Resource
    private QuestionKnowledgePointMapper questionKnowledgePointMapper;
    @Resource
    private ClassExamStatisticsMapper classExamStatisticsMapper;
    @Resource
    private ActiveStudentsMapper activeStudentsMapper;
    @Resource
    private PersonalExamMapper personalExamMapper;

    // 创建线程池用于异步处理
    private final ExecutorService executorService = Executors.newFixedThreadPool(10);

    /**
     * 通用日期格式化方法
     *
     * @param dateTime 需要格式化的日期
     * @param pattern  格式化模式，默认为yyyy-MM-dd HH:mm:ss
     * @return 格式化后的日期字符串
     */
    private String formatDateTime(LocalDateTime dateTime, String pattern) {
        if (dateTime == null) {
            return "";
        }
        if (StrUtil.isBlank(pattern)) {
            pattern = "yyyy-MM-dd HH:mm:ss";
        }
        return DateUtil.format(dateTime, pattern);
    }

    /**
     * 使用默认格式(yyyy-MM-dd HH:mm:ss)格式化日期
     *
     * @param dateTime 需要格式化的日期
     * @return 格式化后的日期字符串
     */
    private String formatDateTime(LocalDateTime dateTime) {
        return formatDateTime(dateTime, null);
    }

    /**
     * 通用日期格式化方法
     *
     * @param date    需要格式化的日期
     * @param pattern 格式化模式，默认为yyyy-MM-dd HH:mm:ss
     * @return 格式化后的日期字符串
     */
    private String formatDate(Date date, String pattern) {
        if (date == null) {
            return "";
        }
        if (StrUtil.isBlank(pattern)) {
            pattern = "yyyy-MM-dd HH:mm:ss";
        }
        return DateUtil.format(date, pattern);
    }

    /**
     * 使用默认格式(yyyy-MM-dd HH:mm:ss)格式化日期
     *
     * @param date 需要格式化的日期
     * @return 格式化后的日期字符串
     */
    private String formatDate(Date date) {
        return formatDate(date, null);
    }

    @Override
    public IPage<RecentExamAnalyticsResult> getRecentExamAnalytics(RecentExamAnalyticsParam param) {
        return examAnalyzeResultMapper.selectRecentExamAnalytics(param.page(), param);
    }

    @Override
    public StudentAnalyticsResult studentInfo(StudentInfoParam param) {
        StudentAnalyticsDto studentAnalyticsDto = examAnalyzeResultMapper.studentInfo(param);
        CommonResponse.ERROR.assertNotNull(studentAnalyticsDto, "学生考情分析报告不存在");
        StudentAnalyticsResult studentAnalyticsResult = new StudentAnalyticsResult();
        BeanUtil.copyProperties(studentAnalyticsDto, studentAnalyticsResult);
        List<ActiveStudentsEntity> allGradeActiveStudentsEntities = activeStudentsMapper.selectList(
                Wrappers.<ActiveStudentsEntity>lambdaQuery()
                        .eq(ActiveStudentsEntity::getGradeId, param.getGradeId())
        );
        if (CollUtil.isEmpty(allGradeActiveStudentsEntities)) {
            return null;
        }

        // 获取年级内所有班级
        List<ActiveStudentsEntity> gradeStudents = activeStudentsMapper.selectList(
                Wrappers.<ActiveStudentsEntity>lambdaQuery()
                        .eq(ActiveStudentsEntity::getGradeId, param.getGradeId())
                        .select(ActiveStudentsEntity::getClassId)
                        .groupBy(ActiveStudentsEntity::getClassId)
        );

        // 获取年级内所有班级ID
        List<Long> gradeClassIds = gradeStudents.stream()
                .map(ActiveStudentsEntity::getClassId)
                .distinct()
                .toList();

        // 计算年级参与考试的有效学生数量（只计算参与人数超过10人的班级）
        List<ClassExamStatistics> classExamStatisticsList = classExamStatisticsMapper.selectList(Wrappers.<ClassExamStatistics>lambdaQuery()
                .eq(ClassExamStatistics::getExamId, studentAnalyticsDto.getExamId())
                .in(ClassExamStatistics::getClassId, gradeClassIds)
                .gt(ClassExamStatistics::getStudentCount, 9));

        Long examGradeStudentCount = classExamStatisticsList.stream()
                .mapToLong(ClassExamStatistics::getStudentCount)
                .sum();

        studentAnalyticsResult.setExamGradeStudentCount(examGradeStudentCount);
        // 获取薄弱知识点
        Result knowledgePointResult = getKnowledgePointResult(param, studentAnalyticsDto);

        UUID examId = studentAnalyticsDto.getExamId();
        Long classId = param.getClassId();
        Long gradeId = param.getGradeId();
        Long studentId = param.getStudentId();

        // 班级排名=当前学生在本班所有学生中的正确率排名
        int classRanking = getStudentClassRanking(classId, studentId, examId);
        // 年级排名=当前学生在年级所有学生中的正确率排名
        int gradeRanking = getStudentGradeRanking(classExamStatisticsList, gradeId, studentId, examId);
        studentAnalyticsResult.setClassRanking(classRanking);
        studentAnalyticsResult.setGradeRanking(gradeRanking);

        // 计算正确率对比描述
        BigDecimal personalCorrectRate = studentAnalyticsDto.getPersonalCorrectRate();
        BigDecimal classCorrectRate = studentAnalyticsDto.getClassCorrectRate();
        String correctRateCompareDesc;
        int compare = personalCorrectRate.compareTo(classCorrectRate);
        BigDecimal diff = personalCorrectRate.subtract(classCorrectRate).abs().setScale(0, RoundingMode.HALF_UP);
        if (compare > 0) {
            correctRateCompareDesc = "超过班级平均正确率 " + diff + "%。";
        } else if (compare < 0) {
            correctRateCompareDesc = "低于班级平均正确率 " + diff + "%。";
        } else {
            correctRateCompareDesc = "与班级平均正确率持平。";
        }
        String knowledgePointNames = StrUtil.join(",", knowledgePointResult.errorKnowledgePointList());
        String resultOverview = String.format("%s同学本次考试正确率为 %s，" +
                        "位列全班第 %s 名，全年级第 %s 名。" +
                        "在 %s 道题中答对 %s 道，%s" +
                        "薄弱知识点有 %s 个 %s。",
                studentAnalyticsDto.getStudentName(),
                personalCorrectRate + "%",
                classRanking,
                gradeRanking,
                studentAnalyticsDto.getAllQuestionCount(),
                studentAnalyticsDto.getAllQuestionCount() - studentAnalyticsDto.getErrorQuestionCount(),
                correctRateCompareDesc,
                knowledgePointResult.errorKnowledgePointCount(), StrUtil.isBlank(knowledgePointNames) ? "" : ":" + knowledgePointNames);
        System.out.println(resultOverview);
        studentAnalyticsResult.setResultOverview(resultOverview);
        return studentAnalyticsResult;
    }

    /**
     * 获取学生在班级中的排名
     */
    private int getStudentClassRanking(Long classId, Long studentId, UUID examId) {
        List<Long> classStudentIds = activeStudentsMapper.selectList(
                        Wrappers.<ActiveStudentsEntity>lambdaQuery().eq(ActiveStudentsEntity::getClassId, classId))
                .stream().map(ActiveStudentsEntity::getStudentId).collect(Collectors.toList());
        List<ExamAnalyzeResult> classExamResults = examAnalyzeResultMapper.selectList(
                Wrappers.<ExamAnalyzeResult>lambdaQuery()
                        .eq(ExamAnalyzeResult::getExamId, examId)
                        .in(ExamAnalyzeResult::getStudentId, classStudentIds)
        );
        classExamResults.sort(Comparator.comparing(
                r -> extractPercentileFromCorrectRate(r.getCorrectRate()), Comparator.reverseOrder()));
        for (int i = 0; i < classExamResults.size(); i++) {
            if (classExamResults.get(i).getStudentId().equals(studentId)) {
                return i + 1;
            }
        }
        return 0;
    }

    /**
     * 获取学生在年级中的排名
     */
    private int getStudentGradeRanking(List<ClassExamStatistics> classExamStatisticsList, Long gradeId, Long studentId, UUID examId) {
        List<Long> classIds = classExamStatisticsList.stream().map(ClassExamStatistics::getClassId).toList();
        List<Long> gradeStudentIds = activeStudentsMapper.selectList(
                        Wrappers.<ActiveStudentsEntity>lambdaQuery().in(ActiveStudentsEntity::getClassId, classIds))
                .stream().map(ActiveStudentsEntity::getStudentId).collect(Collectors.toList());
        List<ExamAnalyzeResult> gradeExamResults = examAnalyzeResultMapper.selectList(
                Wrappers.<ExamAnalyzeResult>lambdaQuery()
                        .eq(ExamAnalyzeResult::getExamId, examId)
                        .in(ExamAnalyzeResult::getStudentId, gradeStudentIds)
        );
        gradeExamResults.sort(Comparator.comparing(
                r -> extractPercentileFromCorrectRate(r.getCorrectRate()), Comparator.reverseOrder()));
        for (int i = 0; i < gradeExamResults.size(); i++) {
            if (gradeExamResults.get(i).getStudentId().equals(studentId)) {
                return i + 1;
            }
        }
        return 0;
    }

    /**
     * 工具方法：从correctRate JSON中提取percentile字段并转为BigDecimal
     */
    private BigDecimal extractPercentileFromCorrectRate(String correctRateJson) {
        if (correctRateJson == null) return BigDecimal.ZERO;
        try {
            Map map = JSONUtil.toBean(correctRateJson, Map.class);
            Object percentile = map.get("percentile");
            if (percentile == null) {
                return BigDecimal.ZERO;
            }
            String value = percentile.toString().replace("%", "").trim();
            return new BigDecimal(value);
        } catch (Exception e) {
            return BigDecimal.ZERO;
        }
    }

    private Result getKnowledgePointResult(StudentInfoParam param, StudentAnalyticsDto studentAnalyticsDto) {
        // 创建异步任务获取知识点分析数据
        CompletableFuture<IPage<StudentKnowledgePointAnalyticsDto>> knowledgePointAnalysisFuture = CompletableFuture.supplyAsync(() ->
                getKnowledgePointAnalysis(KnowledgePointAnalyticeParam
                        .builder()
                        .examId(studentAnalyticsDto.getExamId())
                        .studentId(param.getStudentId())
                        .classId(param.getClassId())
                        .build()), executorService);

        // 等待异步任务完成并处理结果
        try {
            IPage<StudentKnowledgePointAnalyticsDto> knowledgePointAnalysis = knowledgePointAnalysisFuture.get();
            int errorKnowledgePointCount = 0;
            List<String> errorKnowledgePointList = new ArrayList<>();
            if (knowledgePointAnalysis.getTotal() > 0) {
                // 使用并行流进一步提高过滤和处理性能
                List<StudentKnowledgePointAnalyticsDto> list = knowledgePointAnalysis.getRecords().parallelStream()
                        .filter(studentKnowledgePointAnalyticsDto ->
                                studentKnowledgePointAnalyticsDto.getMasteryLevel().compareTo(BigDecimal.valueOf(100)) < 0)
                        .toList();
                errorKnowledgePointCount = list.size();
                // 使用并行流收集知识点名称
                errorKnowledgePointList = list.parallelStream()
                        .map(StudentKnowledgePointAnalyticsDto::getKnowledgePointName)
                        .collect(Collectors.toList());
            }
            return new Result(errorKnowledgePointCount, errorKnowledgePointList);
        } catch (Exception e) {
            // 异常处理
            throw new BaseException("获取知识点分析数据失败");
        }
    }

    private record Result(int errorKnowledgePointCount, List<String> errorKnowledgePointList) {
    }

    @Override
    public List<QuestionTypeStats> answerOverview(Long analyzeId) {
        // 获取学生试卷的所有题目信息
        List<PersonalExamQuestion> questionList = examAnalyzeResultMapper.questionList(analyzeId);

        if (CollUtil.isEmpty(questionList)) {
            return new ArrayList<>();
        }

        // 按题型分组统计
        Map<QuestionTypeEnum, List<PersonalExamQuestion>> questionTypeMap = questionList.stream()
                .collect(Collectors.groupingBy(PersonalExamQuestion::getQuestionType));

        // 计算每种题型的统计数据
        List<QuestionTypeStats> typeStatsList = new ArrayList<>();

        questionTypeMap.forEach((type, questions) -> {
            int totalCount = questions.size();
            int correctCount = (int) questions.stream()
                    .filter(q -> ResultEnum.correct.equals(q.getResult()))
                    .count();
            int mistakeCount = totalCount - correctCount;

            // 计算正确率，保留两位小数
            String correctRate = totalCount > 0 ?
                    new BigDecimal(correctCount).multiply(new BigDecimal("100"))
                            .divide(new BigDecimal(totalCount), 0, RoundingMode.DOWN) + "%" :
                    "0%";

            QuestionTypeStats stats = QuestionTypeStats.builder()
                    .questionType(type)
                    .questionTypeDesc(type.getDesc())
                    .totalCount(totalCount)
                    .correctCount(correctCount)
                    .mistakeCount(mistakeCount)
                    .correctRate(correctRate)
                    .build();

            typeStatsList.add(stats);
        });

        // 按题目总数降序排序
        typeStatsList.sort((o1, o2) -> Integer.compare(o2.getTotalCount(), o1.getTotalCount()));

        return typeStatsList;
    }

    @Override
    public IPage<StudentKnowledgePointAnalyticsDto> getKnowledgePointAnalysis(KnowledgePointAnalyticeParam param) {
        // 查询所有知识点数据，不进行分页
        List<StudentKnowledgePointAnalyticsDto> allKnowledgePoints = knowledgePointMapper.getStudentKnowledgePointDetail(param);
        
        // 按知识点ID进行去重
        Map<UUID, StudentKnowledgePointAnalyticsDto> knowledgePointMap = new HashMap<>();
        for (StudentKnowledgePointAnalyticsDto dto : allKnowledgePoints) {
            knowledgePointMap.putIfAbsent(dto.getKnowledgePointId(), dto);
        }
        List<StudentKnowledgePointAnalyticsDto> distinctKnowledgePoints = new ArrayList<>(knowledgePointMap.values());

        // 查询该学生该考试的所有题目和知识点关系
        LambdaQueryWrapper<QuestionKnowledgePoint> qkpWrapper = new LambdaQueryWrapper<>();
        qkpWrapper.eq(QuestionKnowledgePoint::getExamId, param.getExamId())
                .isNull(QuestionKnowledgePoint::getDeletedAt);
        List<QuestionKnowledgePoint> allKnowledgePointRelations = questionKnowledgePointMapper.selectList(qkpWrapper);

        // 查询该学生该考试的所有做题记录
        LambdaQueryWrapper<PersonalExam> peWrapper = new LambdaQueryWrapper<>();
        peWrapper.eq(PersonalExam::getStudentId, param.getStudentId())
                .eq(PersonalExam::getExamId, param.getExamId())
                .isNull(PersonalExam::getDeletedAt)
                .orderByDesc(PersonalExam::getCreatedAt)
                .last("limit 1");
        PersonalExam personalExam = personalExamMapper.selectOne(peWrapper);

        if (personalExam != null) {
            LambdaQueryWrapper<PersonalExamQuestion> peqWrapper = new LambdaQueryWrapper<>();
            peqWrapper.eq(PersonalExamQuestion::getPersonalExamId, personalExam.getId())
                    .isNull(PersonalExamQuestion::getDeletedAt);
            List<PersonalExamQuestion> examQuestions = personalExamQuestionMapper.selectList(peqWrapper);

            // 按知识点ID分组题目
            Map<UUID, List<QuestionKnowledgePoint>> knowledgePointRelationsMap = allKnowledgePointRelations.stream()
                    .collect(Collectors.groupingBy(QuestionKnowledgePoint::getKnowledgePointId));

            // 创建题目ID到做题结果的映射
            Map<UUID, PersonalExamQuestion> questionResultMap = examQuestions.stream()
                    .collect(Collectors.toMap(PersonalExamQuestion::getQuestionId, q -> q, (q1, q2) -> q1));

            // 在Java代码中计算每个知识点的掌握度
            for (StudentKnowledgePointAnalyticsDto item : distinctKnowledgePoints) {
                List<QuestionKnowledgePoint> relatedQuestions = knowledgePointRelationsMap.getOrDefault(item.getKnowledgePointId(), Collections.emptyList());

                // 计算该知识点下的题目正确率
                int totalQuestions = relatedQuestions.size();
                int correctQuestions = 0;
                Set<Integer> questionSortNos = new HashSet<>();

                for (QuestionKnowledgePoint qkp : relatedQuestions) {
                    PersonalExamQuestion peq = questionResultMap.get(qkp.getQuestionId());
                    if (peq != null) {
                        if (ResultEnum.correct.equals(peq.getResult())) {
                            correctQuestions++;
                        }
                        if (peq.getSortNo() != null) {
                            questionSortNos.add(peq.getSortNo());
                        }
                    }
                }

                // 计算掌握度（正确率）
                BigDecimal masteryLevel = totalQuestions > 0
                        ? BigDecimal.valueOf((double) correctQuestions * 100 / totalQuestions).setScale(0, RoundingMode.HALF_UP)
                        : BigDecimal.ZERO;

                // 设置计算结果
                item.setMasteryLevel(masteryLevel);
                item.setQuestionSortNoList(questionSortNos);
            }
        }

        // 根据掌握度排序（从低到高）
        distinctKnowledgePoints.sort(Comparator.comparing(StudentKnowledgePointAnalyticsDto::getMasteryLevel));
        
        // 如果需要筛选薄弱知识点
        if (param.isWeak()) {
            distinctKnowledgePoints = distinctKnowledgePoints.stream()
                    .filter(item -> item.getMasteryLevel().compareTo(BigDecimal.valueOf(100)) <= 0)
                    .collect(Collectors.toList());
        }
        
        // 手动分页
        Page<StudentKnowledgePointAnalyticsDto> page = param.page();
        long total = distinctKnowledgePoints.size();
        long current = page.getCurrent();
        long size = page.getSize();
        long start = (current - 1) * size;
        long end = Math.min(start + size, total);
        
        List<StudentKnowledgePointAnalyticsDto> pagedList = start < total ? 
                distinctKnowledgePoints.subList((int)start, (int)end) : 
                Collections.emptyList();
        
        // 创建分页结果
        Page<StudentKnowledgePointAnalyticsDto> resultPage = new Page<>(current, size, total);
        resultPage.setRecords(pagedList);
        
        return resultPage;
    }

    @Override
    public List<MathPersonalExamKnowledgePointPo> listWeakKnowledgePointsByExamAnalyzeResultIds(List<Long> examAnalyzeResultIds) {
        List<MathPersonalExamKnowledgePointPo> weakKnowledgePoints = examAnalyzeResultMapper.listWeakKnowledgePointsByExamAnalyzeResultIds(examAnalyzeResultIds);

//        List<UUID> examIds = weakKnowledgePoints.stream().map(MathPersonalExamKnowledgePointPo::getExamId).toList();
//        List<MathExam> mathExams = mathExamMapper.selectList(new LambdaQueryWrapper<MathExam>().in(MathExam::getId, examIds));
//        List<UUID> multiPublisherExamIds = mathExams.stream().filter(mathExam -> null != mathExam.getSource() && ExamSourceEnum.listRealExam().contains(mathExam.getSource()))
//                .map(MathExam::getId)
//                .toList();
        //常规试卷的题目关联多个教材版本的知识点，根据学生做题的教材版本过滤出需要的知识点
        List<MathPersonalExamKnowledgePointPo> filtedWeakKps = weakKnowledgePoints.stream().filter(item -> {
            if (null != item.getExamPublisher() && null != item.getKnowledgePointPublisher()) {
                return item.getExamPublisher().equals(item.getKnowledgePointPublisher());
            }
            return true;
        }).toList();

        return filtedWeakKps;
    }

    @Override
    public IPage<ExamAnalyzeResultResult> pageExamAnalyzeResults(ExamAnalyzeResultParam param) {
        return examAnalyzeResultMapper.listExamAnalyzeResults(param.page(), param);
    }
}
