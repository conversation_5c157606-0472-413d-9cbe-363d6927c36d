package com.joinus.edu.analytics.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.joinus.edu.analytics.mapper.*;
import com.joinus.edu.analytics.model.param.ExamErrorCorrectionFeedbackParam;
import com.joinus.edu.analytics.model.result.ExamErrorCorrectionFeedbackPo;
import com.joinus.edu.analytics.service.ExamErrorCorrectionFeedbackService;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ExamErrorCorrectionFeedbackServiceImpl implements ExamErrorCorrectionFeedbackService {

    @Resource
    private ExamErrorCorrectionFeedbackMapper examErrorCorrectionFeedbackMapper;

    @Override
    public IPage<ExamErrorCorrectionFeedbackPo> getExamCorrectionResultList(ExamErrorCorrectionFeedbackParam param) {
        IPage<ExamErrorCorrectionFeedbackPo> examErrorCorrectionFeedbackList = examErrorCorrectionFeedbackMapper.getExamErrorCorrectionFeedbackList(param.page(), param);
        examErrorCorrectionFeedbackList.getRecords().forEach(item -> {
            ExamErrorCorrectionFeedbackPo po = examErrorCorrectionFeedbackMapper.selectStudentInfoByStudentId(item.getStudentId());
            if  (po != null) {
                item.setGradeName(po.getGradeName());
                item.setSchoolName(po.getSchoolName());
            }
        });
        return examErrorCorrectionFeedbackList;
    }
}
