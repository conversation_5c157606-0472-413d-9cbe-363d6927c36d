package com.joinus.edu.analytics.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.edu.analytics.model.entity.MathStudentStudyPlan;
import com.joinus.edu.analytics.model.result.MathStudentPlanResult;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【math_student_study_plan(学生学习计划)】的数据库操作Service
* @createDate 2025-09-12 16:19:30
*/
public interface MathStudentStudyPlanService extends IService<MathStudentStudyPlan> {

    List<MathStudentPlanResult> listByStudentId(Long studentId);

    void removeByStudentId(Long studentId);
}
