package com.joinus.edu.analytics.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.joinus.edu.analytics.mapper.*;
import com.joinus.edu.analytics.model.dto.*;
import com.joinus.edu.analytics.model.entity.ActiveStudentsEntity;
import com.joinus.edu.analytics.model.entity.ClassExamStatistics;
import com.joinus.edu.analytics.model.entity.ClassKnowledgePointStatistics;
import com.joinus.edu.analytics.model.entity.ExamAnalyzeResult;
import com.joinus.edu.analytics.model.entity.PersonalExam;
import com.joinus.edu.analytics.model.global.ApiResult;
import com.joinus.edu.analytics.model.global.CommonResponse;
import com.joinus.edu.analytics.model.key.GradeKey;
import com.joinus.edu.analytics.model.param.RecentExamParam;
import com.joinus.edu.analytics.model.result.ClassAccuracyComparisonResult;
import com.joinus.edu.analytics.model.result.ClassInfoDataDTO;
import com.joinus.edu.analytics.service.HomeService;
import com.joinus.edu.analytics.utils.OkHttpUtil;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import scala.annotation.meta.param;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: anpy
 * @date: 2025/4/7 14:56
 */
@Service
public class HomeServiceImpl implements HomeService {

    @Value("${basic-api-url:http://127.0.0.1:9091}")
    private String basicApiUrl;
    @Value("${exam_student_count:10}")
    private int examStudentCount;
    @Resource
    private ClassExamStatisticsMapper classExamStatisticsMapper;
    @Resource
    private ActiveStudentsMapper activeStudentsMapper;
    @Resource
    private ClassKnowledgePointStatisticsMapper classKnowledgePointStatisticsMapper;
    @Resource
    private ClassExamQuestionStatisticsMapper classExamQuestionStatisticsMapper;
    @Resource
    private MathExamsMapper mathExamsMapper;
    @Resource
    private PersonalExamMapper personalExamMapper;
    @Resource
    private PersonalExamQuestionMapper personalExamQuestionMapper;
    @Autowired
    private ExamAnalyzeResultMapper examAnalyzeResultMapper;
    @Resource
    private OkHttpUtil okHttpUtil;

    @Override
    public GradeClassGroupDataDTO getClassAndGradeByTeacherId(Long teacherId) {
        // 调用basic-api获取班级列表
        String apiUrl = basicApiUrl + "/edu-analytics-manager/ijx/teacher/classList";
        String response = okHttpUtil.get(apiUrl, Map.of("teacherId", teacherId));
        CommonResponse.ERROR.assertNotEmpty(response, "获取班级列表失败");
        // 解析API响应数据
        ClassInfoDataDTO classInfoDataDTO = JSONUtil.toBean(response, ClassInfoDataDTO.class);
        if (classInfoDataDTO == null || classInfoDataDTO.getCode() != CommonResponse.SUCCESS.getCode()) {
            CommonResponse.assertError("获取班级列表失败");
        }
        // 获取班级列表
        List<ClassInfoDataDTO.DataDTO.ListDTO> classList = classInfoDataDTO.getData().getList();
        if (CollUtil.isEmpty(classList)) {
            return new GradeClassGroupDataDTO();
        }

        // 使用Stream API一次性完成分组和转换
        List<GradeClassGroupDTO> gradeClassGroups = getGradeClassGroupList(classList);


        // 10. 构建并返回最终结果
        GradeClassGroupDataDTO result = new GradeClassGroupDataDTO();
        result.setList(gradeClassGroups);
        return result;
    }

    /**
     * 根据年级id和年级名称分组和转换
     *
     * @param classList 所有班级列表
     * @return 分组后的年级、班级列表
     */
    private static List<GradeClassGroupDTO> getGradeClassGroupList(List<ClassInfoDataDTO.DataDTO.ListDTO> classList) {
        // 使用LinkedHashMap保持年级的顺序
        Map<GradeKey, List<ClassInfoDataDTO.DataDTO.ListDTO>> gradeGroups = new LinkedHashMap<>();

        // 按照原始列表顺序进行分组
        for (ClassInfoDataDTO.DataDTO.ListDTO classInfo : classList) {
            GradeKey gradeKey = new GradeKey(classInfo.getGradeId(), classInfo.getGradeName());
            gradeGroups.computeIfAbsent(gradeKey, k -> new ArrayList<>()).add(classInfo);
        }

        // 转换为最终结果
        return gradeGroups.entrySet().stream()
                .map(entry -> {
                    GradeClassGroupDTO groupDTO = new GradeClassGroupDTO();
                    groupDTO.setGradeId(entry.getKey().getGradeId());
                    groupDTO.setGradeName(entry.getKey().getGradeName());
                    groupDTO.setClassList(entry.getValue().stream()
                            .map(classInfo -> GradeClassListDTO.builder()
                                    .classId(classInfo.getClassId())
                                    .className(classInfo.getClassName())
                                    .build())
                            .collect(Collectors.toList()));
                    return groupDTO;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<WeakKnowledgePointDTO> getWeakKnowledgePoints(List<Long> classIds, String type, UUID examId) {
        // 班级掌握率低于 60%的知识点
        double correctRateThreshold = 60.0;
        // 最多展示 5 条数据
        int limit = 0;
        if ("home".equals(type)) {
            limit = 5;
            ClassExamStatistics classExamStatistics = classExamStatisticsMapper.
                    selectOne(Wrappers.lambdaQuery(ClassExamStatistics.class)
                            .in(ClassExamStatistics::getClassId, classIds)
                            .gt(ClassExamStatistics::getStudentCount, examStudentCount)
                            .orderByDesc(ClassExamStatistics::getCreatedAt).last("limit 1"));

            if (classExamStatistics == null) {
                return new ArrayList<>();
            }

            examId = classExamStatistics.getExamId();
        }

        return classKnowledgePointStatisticsMapper.getWeakKnowledgePoints(classIds, correctRateThreshold, limit, examId);
    }

    @Override
    public IPage<RecentExamDTO> getRecentExams(RecentExamParam param) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -1);
        Date oneMonthAgo = calendar.getTime();
        //分页查询班级相关考试信息
        IPage<RecentExamDTO> examStatisticsPage = classExamStatisticsMapper.getExamStatisticsList(param.page(), param.getClassIds(), oneMonthAgo);
        if (examStatisticsPage.getTotal() == 0) {
            return examStatisticsPage;
        }

        examStatisticsPage.getRecords().forEach(recentExamDTO -> {
            UUID examId = recentExamDTO.getExamId();
            //单场考试对应的班级列表
            List<ClassExamStatistics> classExamStatisticsList = classExamStatisticsMapper.selectList(
                    Wrappers.<ClassExamStatistics>lambdaQuery()
                            .eq(ClassExamStatistics::getExamId, examId)
                            .in(ClassExamStatistics::getClassId, param.getClassIds()));
            List<ClassExamDataDTO> classDataList = new ArrayList<>();
            classExamStatisticsList.forEach(classExamStatistics -> {
                ActiveStudentsEntity activeStudentsEntity = activeStudentsMapper.selectOne(
                        Wrappers.<ActiveStudentsEntity>lambdaQuery()
                                .eq(ActiveStudentsEntity::getClassId, classExamStatistics.getClassId())
                                .last("LIMIT 1")
                );
                if (classExamStatistics.getStudentCount() > examStudentCount) {
                    ClassExamDataDTO classData = createClassData(examId, classExamStatistics, activeStudentsEntity);
                    classDataList.add(classData);
                }

            });
            recentExamDTO.setClassDataList(classDataList);
        });

        return examStatisticsPage;
    }

    /**
     * 生成班级考试数据
     */
    private ClassExamDataDTO createClassData(UUID examId, ClassExamStatistics classStatistics, ActiveStudentsEntity activeStudentsEntity) {
        Long classId = activeStudentsEntity.getClassId();
        // 创建班级考试数据DTO对象
        ClassExamDataDTO classExamData = ClassExamDataDTO.builder()
                .classId(classId)
                .className(activeStudentsEntity.getClassName())
                .build();

        // 设置总学生数
        List<ActiveStudentsEntity> totalStudents = activeStudentsMapper.selectList(Wrappers.<ActiveStudentsEntity>lambdaQuery()
                .eq(ActiveStudentsEntity::getClassId, classId));
        classExamData.setTotalStudents(totalStudents.size());

        List<Long> collect = totalStudents.stream().map(ActiveStudentsEntity::getStudentId).toList();
        List<ExamAnalyzeResult> examAnalyzeResults = examAnalyzeResultMapper.selectList(Wrappers.<ExamAnalyzeResult>lambdaQuery()
                .eq(ExamAnalyzeResult::getExamId, examId)
                .in(ExamAnalyzeResult::getStudentId, collect));
        List<Long> examStudentIdList = examAnalyzeResults.stream().map(ExamAnalyzeResult::getStudentId).toList();

        // 设置参与学生数 - 使用班级考试统计中的学生数
        classExamData.setParticipatedStudents(classStatistics.getStudentCount());

        // 设置正确率
        classExamData.setCorrectRate(Math.round(classStatistics.getCorrectRate().doubleValue()));

        // 计算平均错题数
        Integer avgWrongQuestions = calculateAvgWrongQuestions(examId, examStudentIdList);
        classExamData.setAvgWrongQuestions(avgWrongQuestions);

        // 计算薄弱知识点数
        Integer weakKnowledgePoints = countWeakKnowledgePoints(classId, examId);
        classExamData.setWeakKnowledgePoints(weakKnowledgePoints);
        return classExamData;
    }

    /**
     * 计算班级在某次考试中的平均错题数
     *
     * @param examId            考试ID
     * @param examStudentIdList 班级中参与考试的学生
     * @return 平均错题数
     */
    private Integer calculateAvgWrongQuestions(UUID examId, List<Long> examStudentIdList) {
        // 获取所有学生的个人试卷ID，每个学生只获取最后一次考试记录
        Map<Long, PersonalExam> studentLastExamMap = new HashMap<>();

        // 查询所有符合条件的个人试卷
        List<PersonalExam> allPersonalExams = personalExamMapper.selectList(
                Wrappers.<PersonalExam>lambdaQuery()
                        .eq(PersonalExam::getExamId, examId)
                        .in(PersonalExam::getStudentId, examStudentIdList)
                        .orderByDesc(PersonalExam::getCreatedAt)
        );

        // 对每个学生只保留最后一次考试记录
        for (PersonalExam exam : allPersonalExams) {
            if (!studentLastExamMap.containsKey(exam.getStudentId())) {
                studentLastExamMap.put(exam.getStudentId(), exam);
            }
        }

        // 获取所有个人试卷ID（去重后）
        List<Long> personalExamIds = studentLastExamMap.values().stream()
                .map(PersonalExam::getId)
                .toList();

        // 计算总错题数
        int totalWrongQuestions = 0;
        for (Long personalExamId : personalExamIds) {
            // 获取每个学生的错题数
            Integer wrongCount = personalExamQuestionMapper.countWrongQuestionsByPersonalExamId(personalExamId);
            if (wrongCount != null) {
                totalWrongQuestions += wrongCount;
            }
        }

        // 计算平均错题数（总错题数除以参与考试的学生数）
        return examStudentIdList.isEmpty() ? 0 : (int) Math.floor((float) totalWrongQuestions / studentLastExamMap.size());
    }

    /**
     * 统计班级在某次考试中的薄弱知识点数量
     * 薄弱知识点定义：正确率低于60%的知识点
     *
     * @param classId 班级ID
     * @param examId  考试ID
     * @return 薄弱知识点数量
     */
    private Integer countWeakKnowledgePoints(Long classId, UUID examId) {
        // 查询知识点统计数据
        return classKnowledgePointStatisticsMapper.selectCount(
                Wrappers.<ClassKnowledgePointStatistics>lambdaQuery()
                        .eq(ClassKnowledgePointStatistics::getClassId, classId)
                        .eq(ClassKnowledgePointStatistics::getExamId, examId)
                        .lt(ClassKnowledgePointStatistics::getCorrectRate, 60)
                        .isNull(ClassKnowledgePointStatistics::getDeletedAt)
        ).intValue();
    }

    @Override
    public List<ClassAccuracyComparisonResult> getClassAccuracyComparison(List<Long> classIds) {
        List<ClassAccuracyComparisonResult> resultList = new ArrayList<>();
        //获取最近一次考试
        ClassExamStatistics classExamStatistics = classExamStatisticsMapper.
                selectOne(Wrappers.lambdaQuery(ClassExamStatistics.class)
                        .in(ClassExamStatistics::getClassId, classIds)
                        .gt(ClassExamStatistics::getStudentCount, examStudentCount)
                        .orderByDesc(ClassExamStatistics::getCreatedAt).last("limit 1"));
        if (classExamStatistics == null) {
            return resultList;
        }

        UUID examId = classExamStatistics.getExamId();
        List<ClassExamStatistics> classExamStatisticsList = classExamStatisticsMapper.
                selectList(Wrappers.lambdaQuery(ClassExamStatistics.class)
                        .in(ClassExamStatistics::getClassId, classIds)
                        .eq(ClassExamStatistics::getExamId, examId)
                        .gt(ClassExamStatistics::getStudentCount, examStudentCount));

        classExamStatisticsList.forEach(item -> {
            ActiveStudentsEntity activeStudentsEntity = activeStudentsMapper.selectOne(Wrappers.lambdaQuery(ActiveStudentsEntity.class)
                    .eq(ActiveStudentsEntity::getClassId, item.getClassId()).last("limit 1"));
            resultList.add(ClassAccuracyComparisonResult
                    .builder()
                    .classId(activeStudentsEntity.getClassId())
                    .className(activeStudentsEntity.getClassName())
                    .correctRate(item.getCorrectRate().doubleValue())
                    .build());
        });
        return resultList;
    }

    @Override
    public GradeClassGroupDataDTO classesInfo(List<Long> classIds) {
        // 将classIds转换为逗号分隔的字符串
        String classIdsStr = classIds.stream()
                .map(String::valueOf)
                .collect(Collectors.joining(","));

        Map<String, Object> params = new HashMap<>();
        params.put("classIds", classIdsStr);

        String response = okHttpUtil.get(basicApiUrl + "/edu-analytics-manager/ijx/classes/student/count", params);
        ApiResult apiResult = JSONUtil.toBean(response, ApiResult.class);
        Integer studentNum = 0;
        if (apiResult.getData() != null) {
            studentNum = (Integer) apiResult.getData();
        }

        // 获取所有符合条件的考试统计信息
        List<ClassExamStatistics> allExamStatistics = classExamStatisticsMapper.selectList(Wrappers.<ClassExamStatistics>lambdaQuery()
                .in(ClassExamStatistics::getClassId, classIds)
                .gt(ClassExamStatistics::getStudentCount, examStudentCount));

        // 按examId去重，只保留每个考试的一条记录
        Set<UUID> uniqueExamIds = allExamStatistics.stream()
                .map(ClassExamStatistics::getExamId)
                .collect(Collectors.toSet());

        // 统计去重后的考试总数
        Long examCount = (long) uniqueExamIds.size();

        return GradeClassGroupDataDTO.builder()
                .studentNum(studentNum == null? 0 : studentNum)
                .examNum(examCount)
                .build();
    }
}
