package com.joinus.edu.analytics.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.edu.analytics.mapper.MathStudentInfoMapper;
import com.joinus.edu.analytics.model.dto.StudentInfoPlanDto;
import com.joinus.edu.analytics.model.entity.MathStudentInfo;
import com.joinus.edu.analytics.model.enums.GradeEnum;
import com.joinus.edu.analytics.model.param.StudentInfoParam;
import com.joinus.edu.analytics.service.MathStudentInfoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class MathStudentInfoServiceImpl  extends ServiceImpl<MathStudentInfoMapper, MathStudentInfo>
        implements MathStudentInfoService{
    @Override
    public IPage<StudentInfoPlanDto> getStudentPlanPage(StudentInfoParam param) {
        IPage<StudentInfoPlanDto> page = param.page();
        if(StringUtils.isNotBlank(param.getParentTelNum())){
            // 根据手机号码查询 学生id
            param.setParentName("家长姓名");
        }
        page = baseMapper.getStudentPlanPage(page, param);
        if (StringUtils.isNotBlank(param.getParentTelNum())) {
            page.getRecords().forEach(item -> {
                item.setGradeName(GradeEnum.ofValue(item.getGrade()).name());
                item.setParentName(param.getParentName());
                item.setParentTelNum(param.getParentTelNum());
            });
        } else {
            List<Long> studentIds = page.getRecords().stream().map(item -> item.getStudentId()).collect(Collectors.toList());
            if(studentIds.size() > 0){
                // 根据学生id查询家长信息
                page.getRecords().forEach(item -> {
                    item.setGradeName(GradeEnum.ofValue(item.getGrade()).name());
                   /* item.setParentName(param.getParentName());
                    item.setParentTelNum(param.getParentTelNum());*/
                });
            }
        }
        return page;
    }
    @Override
    public boolean changeStudentPlanStatus(Long studentId, Boolean studyPlanEnabled) {
        return baseMapper.updateStudentPlanStatus(studentId, studyPlanEnabled);
    }
}
