package com.joinus.edu.analytics.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.joinus.edu.analytics.model.dto.GradeClassGroupDataDTO;
import com.joinus.edu.analytics.model.dto.RecentExamDTO;
import com.joinus.edu.analytics.model.dto.WeakKnowledgePointDTO;
import com.joinus.edu.analytics.model.param.RecentExamParam;
import com.joinus.edu.analytics.model.result.ClassAccuracyComparisonResult;

import java.util.List;
import java.util.UUID;

/**
 * @Description: 首页服务
 * @Author:  anpy
 * @date:  2025/4/16 16:24
 */
public interface HomeService {

    /**
     * 根据教师id获取班级年级信息
     *
     * @param teacherId 教师id
     * @return 按年级名称和年级ID分组的班级信息
     */
    GradeClassGroupDataDTO getClassAndGradeByTeacherId(Long teacherId);
    
    /**
     * 获取掌握度低于60%的薄弱知识点
     *
     * @param classIds 班级id列表
     * @param type
     * @param examId
     * @return 薄弱知识点列表
     */
    List<WeakKnowledgePointDTO> getWeakKnowledgePoints(List<Long> classIds, String type, UUID examId);
    
    /**
     * 获取一个月内人数超过10的试卷
     * @param param     请求参数
     *  classIds 班级列表
     *  current  页码
     *  size 每页大小
     * @return 试卷分页列表
     */
    IPage<RecentExamDTO> getRecentExams(RecentExamParam param);

    /**
     * 班级平均正确率对比
     * @param classIds 班级列表
     * @return 对比结果列表
     */
    List<ClassAccuracyComparisonResult> getClassAccuracyComparison(List<Long> classIds);

    GradeClassGroupDataDTO classesInfo(List<Long> classIds);
}
