package com.joinus.edu.analytics.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.edu.analytics.mapper.MathKnowledgePointMapper;
import com.joinus.edu.analytics.model.entity.MathKnowledgePoint;
import com.joinus.edu.analytics.model.po.MathKnowledgePointPo;
import com.joinus.edu.analytics.service.MathKnowledgePointService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

@Service
public class MathKnowledgePointServiceImpl extends ServiceImpl<MathKnowledgePointMapper, MathKnowledgePoint> implements MathKnowledgePointService {


    @Override
    public List<MathKnowledgePointPo> listByIds(List<UUID> kpIds) {
        return baseMapper.listByIds(kpIds);
    }
}
