package com.joinus.edu.analytics.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.afs.model.v20180112.AnalyzeNvcRequest;
import com.aliyuncs.afs.model.v20180112.AnalyzeNvcResponse;
import com.aliyuncs.exceptions.ClientException;
import com.joinus.edu.analytics.exception.BaseException;
import com.joinus.edu.analytics.model.dto.MathCatalogNodeDto;
import com.joinus.edu.analytics.model.dto.MathKnowledgePointDto;
import com.joinus.edu.analytics.model.enums.PublisherEnum;
import com.joinus.edu.analytics.model.result.MathKnowledgePointResult;
import com.joinus.edu.analytics.service.CaptchaService;
import com.joinus.edu.analytics.service.EduKnowledgeHubService;
import com.joinus.edu.analytics.utils.LocalHttpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class EduKnowledgeHubServiceImpl implements EduKnowledgeHubService {

//    @Value("${edu-knowledge-hub-host-url:https://edu-knowledge-hub.uat.qingyulan.net}")
    @Value("${edu-knowledge-hub-host-url:http://127.0.0.1:8080}")
    private String eduKnowLedgeHubHostUrl;

    @Override
    public List<MathCatalogNodeDto> listCatalogNodes(Integer grade, PublisherEnum publisher, Integer semester) {
        String url = StrUtil.format("/api/edu-knowledge-hub/ai/ability/math/catalogs/nodes?grade={}&publisher={}&semester={}",
                grade, publisher, semester);
        String response = LocalHttpUtil.get(eduKnowLedgeHubHostUrl + url , null);
        JSONObject responseBody = JSONUtil.parseObj(response);
        if (responseBody.getInt("code") != 200) {
            throw new BaseException(responseBody.getStr("message"));
        }
        return JSONUtil.toList(responseBody.getJSONArray("data"), MathCatalogNodeDto.class);
    }

    @Override
    public List<MathKnowledgePointDto> listKnowledgePointsByNodeIds(Set<UUID> nodeIds) {
        Map<String, Object> params = new HashMap<>(1);
        params.put("sectionIds", nodeIds);
        String response = LocalHttpUtil.get(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/math/sections/knowledge-points",params);
        JSONObject responseBody = JSONUtil.parseObj(response);
        if (responseBody.getInt("code") != 200) {
            throw new BaseException(responseBody.getStr("message"));
        }
        return JSONUtil.toList(responseBody.getJSONArray("data"), MathKnowledgePointDto.class);

    }

    @Override
    public List<MathCatalogNodeDto> listCatalogNodesByKpIds(List<UUID> weakKpIds) {
        String url = StrUtil.format("/api/edu-knowledge-hub/ai/ability/math/catalogs/nodes/by-knowledge-points");
        Map<String, Object> params = new HashMap<>(1);
        params.put("knowledgePointIds", weakKpIds);
        String response = LocalHttpUtil.get(eduKnowLedgeHubHostUrl + url , params);
        JSONObject responseBody = JSONUtil.parseObj(response);
        if (responseBody.getInt("code") != 200) {
            throw new BaseException(responseBody.getStr("message"));
        }
        return JSONUtil.toList(responseBody.getJSONArray("data"), MathCatalogNodeDto.class);
    }

}
