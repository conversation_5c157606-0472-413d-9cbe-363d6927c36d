package com.joinus.edu.analytics.service;

import cn.hutool.json.JSONObject;
import com.joinus.edu.analytics.model.enums.BookVolumeEnum;
import com.joinus.edu.analytics.model.enums.PublisherEnum;
import com.joinus.edu.analytics.model.param.CreateStudentPlanParam;
import com.joinus.edu.analytics.model.param.PreviewStudentPlanParam;
import com.joinus.edu.analytics.model.result.MathBookNodeResult;
import com.joinus.edu.analytics.model.result.MathCatalogNodeResult;
import com.joinus.edu.analytics.model.result.MathStudentPlanResult;

import java.util.List;

public interface StudyPlanService {

    List<MathCatalogNodeResult> listKnowlwdgePointsTree(PublisherEnum publisher, Integer grade, BookVolumeEnum bookVolume);

    List<MathBookNodeResult> listKnowlwdgePointsTreeByExamAnalyzeResultIds(List<Long>  examAnalyzeResultId);

    List<JSONObject> listBooksTree();

    List<MathStudentPlanResult> previewStudentPlan(PreviewStudentPlanParam param);

    List<MathStudentPlanResult> createStudentPlan(CreateStudentPlanParam param);
}
