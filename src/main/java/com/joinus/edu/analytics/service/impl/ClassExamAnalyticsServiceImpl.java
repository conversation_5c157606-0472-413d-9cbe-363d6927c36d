package com.joinus.edu.analytics.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.joinus.edu.analytics.mapper.*;
import com.joinus.edu.analytics.model.dto.AllExamClassInfoDto;
import com.joinus.edu.analytics.model.dto.ClassQuestionStatisticsDto;
import com.joinus.edu.analytics.model.dto.ExamDetailsDto;
import com.joinus.edu.analytics.model.entity.*;
import com.joinus.edu.analytics.model.global.CommonResponse;
import com.joinus.edu.analytics.model.param.KnowledgePointAnalyticeParam;
import com.joinus.edu.analytics.model.param.QuestionAnalysisParam;
import com.joinus.edu.analytics.model.param.StudentAnalyticsParam;
import com.joinus.edu.analytics.model.result.ClassKnowledgePointAnalyticsResult;
import com.joinus.edu.analytics.model.result.ExamDetailsResult;
import com.joinus.edu.analytics.model.result.TopWrongQuestionResult;
import com.joinus.edu.analytics.service.ClassExamAnalyticsService;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 学生考试分析服务实现类
 * @Author: anpy
 * @date: 2025/4/16 20:00
 */
@Service
@RequiredArgsConstructor
public class ClassExamAnalyticsServiceImpl implements ClassExamAnalyticsService {
    @Value("${exam_student_count:10}")
    private int examStudentCount;
    @Resource
    private ExamAnalyzeResultMapper examAnalyzeResultMapper;
    @Resource
    private ClassExamQuestionStatisticsMapper classExamQuestionStatisticsMapper;
    @Resource
    private ClassExamStatisticsMapper classExamStatisticsMapper;
    @Resource
    private PersonalExamQuestionMapper personalExamQuestionMapper;
    @Resource
    private QuestionKnowledgePointMapper questionKnowledgePointMapper;
    @Resource
    private ActiveStudentsMapper activeStudentsMapper;
    @Resource
    private KnowledgePointMapper knowledgePointMapper;

    /**
     * 获取学生考试报告
     * <p>
     * examId   考试ID
     * classId  班级ID
     * pageNum  页码
     * pageSize 每页大小
     *
     * @return 分页数据，包含学生考试报告列表
     */
    @Override
    public IPage<ClassStudentExamResultEntity> getStudentExamReport(StudentAnalyticsParam param) {
        return examAnalyzeResultMapper.getClassStudentsWithExamResults(param.page(), param);
    }

    @Override
    public List<TopWrongQuestionResult> getTopWrongQuestions(UUID examId, Long classId) {
        ClassExamStatistics classExamStatistics = classExamStatisticsMapper.selectOne(Wrappers.<ClassExamStatistics>lambdaQuery()
                .eq(ClassExamStatistics::getExamId, examId)
                .eq(ClassExamStatistics::getClassId, classId)
                .last("LIMIT 1"));
        CommonResponse.ERROR.assertNotNull(classExamStatistics, "班级考试统计信息不存在");

        List<ClassExamQuestionStatistics> classExamQuestionStatistics = classExamQuestionStatisticsMapper.selectList(
                Wrappers.<ClassExamQuestionStatistics>lambdaQuery()
                        .eq(ClassExamQuestionStatistics::getClassId, classId)
                        .eq(ClassExamQuestionStatistics::getExamId, examId)
                        .lt(ClassExamQuestionStatistics::getCorrectRate, 100)
                        .orderByAsc(ClassExamQuestionStatistics::getCorrectRate)
                        .last("LIMIT 5")
        );

        List<TopWrongQuestionResult> resultList = new ArrayList<>();
        // 考试人数
        Integer studentCount = classExamStatistics.getStudentCount();

        classExamQuestionStatistics.forEach(questionStatistics -> {
            //获取题型
            PersonalExamQuestion personalExamQuestion = personalExamQuestionMapper.selectOne(Wrappers.<PersonalExamQuestion>lambdaQuery()
                    .eq(PersonalExamQuestion::getQuestionId, questionStatistics.getQuestionId()).last("LIMIT 1"));
            //获取知识点
            List<QuestionKnowledgePoint> questionKnowledgePoints = questionKnowledgePointMapper.selectList(Wrappers.<QuestionKnowledgePoint>lambdaQuery()
                    .eq(QuestionKnowledgePoint::getQuestionId, questionStatistics.getQuestionId()).eq(QuestionKnowledgePoint::getExamId, examId));
            Set<String> knowledgePointSet = new HashSet<>();
            if (CollUtil.isNotEmpty(questionKnowledgePoints)) {
                // 知识点集合
                List<String> knowledgePointsList = questionKnowledgePoints.stream().map(QuestionKnowledgePoint::getKnowledgePointName).toList();
                knowledgePointSet = new HashSet<>(knowledgePointsList);
            }
            TopWrongQuestionResult topWrongQuestionResult = TopWrongQuestionResult.builder()
                    .questionType(personalExamQuestion.getQuestionType().getDesc())
                    .errorStudentCount(studentCount - questionStatistics.getCorrectCount())
                    .errorRate(100 - questionStatistics.getCorrectRate().doubleValue())
                    .questionId(questionStatistics.getQuestionId())
                    .knowledgePoints(knowledgePointSet)
                    .sortNo(questionStatistics.getSortNo())
                    .build();
            resultList.add(topWrongQuestionResult);
        });

        return resultList;
    }

    @Override
    public IPage<ClassQuestionStatisticsDto> getQuestionAnalysis(QuestionAnalysisParam param) {
        IPage<ClassQuestionStatisticsDto> questionStatisticsPage;
        if (param.getPersonalExamId() != null) {
            questionStatisticsPage = classExamQuestionStatisticsMapper.getStudentQuestionStatistics(param.page(), param);
        } else {
            questionStatisticsPage = classExamQuestionStatisticsMapper.getClassQuestionStatistics(param.page(), param);
        }

        List<ClassQuestionStatisticsDto> questionStatisticsList = questionStatisticsPage.getRecords();

        // 获取所有题目ID
        List<UUID> allQuestionIds = questionStatisticsList.stream()
                .map(ClassQuestionStatisticsDto::getQuestionId)
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(allQuestionIds)) {
            return questionStatisticsPage;
        }

        // 查询题目类型
        Map<UUID, PersonalExamQuestion> questionTypeMap = personalExamQuestionMapper.selectList(
                Wrappers.<PersonalExamQuestion>lambdaQuery()
                        .in(PersonalExamQuestion::getQuestionId, allQuestionIds)
        ).stream().collect(Collectors.toMap(
                PersonalExamQuestion::getQuestionId,
                question -> question,
                (q1, q2) -> q1
        ));


        for (ClassQuestionStatisticsDto statistics : questionStatisticsList) {
            UUID questionId = statistics.getQuestionId();
            PersonalExamQuestion question = questionTypeMap.get(questionId);
            statistics.setQuestionType(question != null ? question.getQuestionType().getDesc() : "未知");
        }

        return questionStatisticsPage;
    }

    @Override
    public IPage<ClassKnowledgePointAnalyticsResult> getKnowledgePointAnalysis(KnowledgePointAnalyticeParam param) {
        return knowledgePointMapper.getKnowledgePointAnalysis(param.page(), param);
    }

    @Override
    public ExamDetailsResult getExamDetails(UUID examId, Long classId) {
        ExamDetailsDto examDetails = classExamStatisticsMapper.getExamDetails(examId, classId);
        CommonResponse.ERROR.assertNotNull(examDetails, "获取试卷详情失败");

        //该年级下生成分析报告的班级列表
        List<AllExamClassInfoDto> allExamClassInfoList = classExamStatisticsMapper.getAllExamClassInfo(examDetails.getGradeId());
        Set<Long> classIds = allExamClassInfoList.stream().map(AllExamClassInfoDto::getClassId).collect(Collectors.toSet());
        if (CollUtil.isEmpty(classIds)) {
            CommonResponse.assertError("该年级下没有生成分析报告的班级");
        }
        // 获取该年级下所有学生列表
        List<ActiveStudentsEntity> activeStudentsEntities = activeStudentsMapper.selectList(Wrappers.<ActiveStudentsEntity>lambdaQuery().
                in(ActiveStudentsEntity::getClassId, classIds));

        Set<Long> studentIds = activeStudentsEntities.stream().map(ActiveStudentsEntity::getStudentId).collect(Collectors.toSet());

        //获取该年级下所有参与考试的学生列表
        List<ExamAnalyzeResult> allExamAnalyzeResults = examAnalyzeResultMapper.selectList(Wrappers.<ExamAnalyzeResult>lambdaQuery()
                .eq(ExamAnalyzeResult::getExamId, examId)
                .in(ExamAnalyzeResult::getStudentId, studentIds));

        // 对同一个学生只保留updated_at最新的一条数据
        Map<Long, ExamAnalyzeResult> latestResultMap = allExamAnalyzeResults.stream()
                .collect(Collectors.toMap(
                        ExamAnalyzeResult::getStudentId,
                        result -> result,
                        (existing, replacement) -> existing.getCreatedAt().isBefore(replacement.getCreatedAt()) ? replacement : existing
                ));

        Set<ExamAnalyzeResult> examAnalyzeResults = new HashSet<>(latestResultMap.values());

        // 计算每个班级参与考试的学生数量
        Map<Long, Long> classStudentCountMap = examAnalyzeResults.stream()
                .collect(Collectors.groupingBy(
                        result -> activeStudentsEntities.stream()
                                .filter(student -> student.getStudentId().equals(result.getStudentId()))
                                .findFirst()
                                .map(ActiveStudentsEntity::getClassId)
                                .orElse(0L),
                        Collectors.counting()
                ));

        // 筛选出至少有10个学生生成报告的班级，并按正确率降序排序
        List<AllExamClassInfoDto> validClassList = allExamClassInfoList.stream()
                .filter(classInfo -> classStudentCountMap.getOrDefault(classInfo.getClassId(), 0L) >= examStudentCount)
                .sorted(Comparator.comparing(AllExamClassInfoDto::getCorrectRate).reversed())
                .toList();

        // 计算当前班级排名
        int rank = 0;
        for (int i = 0; i < validClassList.size(); i++) {
            if (validClassList.get(i).getClassId().equals(classId)) {
                rank = i + 1;
                break;
            }
        }
        //studentCount相加
        Integer allExamStudentCount = validClassList.stream().map(AllExamClassInfoDto::getStudentCount).reduce(Integer::sum).orElse(0);
        ExamDetailsResult examDetailsResult = new ExamDetailsResult();
        BeanUtil.copyProperties(examDetails, examDetailsResult);
        //参与考试当前年级的所有人数
        examDetailsResult.setAllExamStudentCount(allExamStudentCount);
        examDetailsResult.setClassCount(validClassList.size());
        examDetailsResult.setRanking(rank);

        return examDetailsResult;
    }
}
