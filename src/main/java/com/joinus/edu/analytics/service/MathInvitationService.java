package com.joinus.edu.analytics.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.joinus.edu.analytics.model.param.MathInvitationParam;
import com.joinus.edu.analytics.model.result.MathInvitationPo;

/**
* @description 针对表【math_invitation(数学邀请记录表)】的数据库操作Service
*/
public interface MathInvitationService {

    IPage<MathInvitationPo> getMathInvitationResultList(MathInvitationParam param);
}
