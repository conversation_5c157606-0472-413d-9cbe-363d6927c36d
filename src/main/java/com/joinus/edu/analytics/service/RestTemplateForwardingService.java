package com.joinus.edu.analytics.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 使用RestTemplate实现的请求转发服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RestTemplateForwardingService {

    private final RestTemplate restTemplate;

    @Value("${forwarding.target-service-url:http://localhost:8080}")
    private String targetServiceUrl;

    /**
     * 转发请求到目标服务
     *
     * @param method       HTTP方法
     * @param path         请求路径
     * @param headers      请求头
     * @param queryParams  查询参数
     * @param requestBody  请求体
     * @return 响应实体
     */
    public ResponseEntity<byte[]> forwardRequest(
            HttpMethod method,
            String path,
            Map<String, String> headers,
            Map<String, List<String>> queryParams,
            Object requestBody
    ) {
        try {
            // 构建目标URL
            String fullUrl = targetServiceUrl + path;
            log.info("转发{}请求到: {}", method, fullUrl);

            // 构建URI，添加查询参数
            UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(fullUrl);
            if (queryParams != null && !queryParams.isEmpty()) {
                queryParams.forEach((key, values) -> {
                    if (key != null && values != null && !values.isEmpty()) {
                        for (String value : values) {
                            if (value != null) {
                                uriBuilder.queryParam(key, value);
                            }
                        }
                    }
                });
            }
            URI uri = uriBuilder.build().encode().toUri();
            log.info("最终请求URI: {}", uri);

            // 构建请求头
            HttpHeaders httpHeaders = new HttpHeaders();
            if (headers != null && !headers.isEmpty()) {
                headers.forEach((key, value) -> {
                    if (key != null && value != null && !key.equalsIgnoreCase("host")) {
                        httpHeaders.add(key, value);
                    }
                });
            }
            
            // 设置接受所有媒体类型
            httpHeaders.setAccept(Collections.singletonList(MediaType.ALL));
            
            // 如果是表单提交，设置相应的内容类型
            if (requestBody instanceof MultiValueMap) {
                httpHeaders.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            }

            // 构建请求实体
            HttpEntity<?> requestEntity;
            if (requestBody != null) {
                requestEntity = new HttpEntity<>(requestBody, httpHeaders);
            } else {
                requestEntity = new HttpEntity<>(httpHeaders);
            }

            log.info("发送请求: {} {}", method, uri);
            log.debug("请求头: {}", httpHeaders);
            
            // 执行请求
            ResponseEntity<byte[]> responseEntity = restTemplate.exchange(
                    uri,
                    method,
                    requestEntity,
                    byte[].class
            );

            log.info("收到响应: 状态码={}", responseEntity.getStatusCode());
            return responseEntity;
        } catch (HttpStatusCodeException e) {
            log.error("转发请求失败，HTTP错误: {} - {}", e.getStatusCode(), e.getStatusText());
            // 返回错误响应
            return ResponseEntity
                    .status(e.getStatusCode())
                    .headers(e.getResponseHeaders())
                    .body(e.getResponseBodyAsByteArray());
        } catch (Exception e) {
            log.error("转发请求失败: {}", e.getMessage(), e);
            // 返回500错误
            return ResponseEntity
                    .status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(("转发请求失败: " + e.getMessage()).getBytes());
        }
    }

    /**
     * 将Map<String, List<String>>转换为MultiValueMap
     */
    public MultiValueMap<String, String> convertToMultiValueMap(Map<String, List<String>> map) {
        MultiValueMap<String, String> multiValueMap = new LinkedMultiValueMap<>();
        if (map != null) {
            map.forEach((key, values) -> {
                if (key != null && values != null) {
                    for (String value : values) {
                        if (value != null) {
                            multiValueMap.add(key, value);
                        }
                    }
                }
            });
        }
        return multiValueMap;
    }
}
