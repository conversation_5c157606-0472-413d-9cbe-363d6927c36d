package com.joinus.edu.analytics.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.edu.analytics.mapper.PersonalExamMapper;
import com.joinus.edu.analytics.model.dto.PersonalExamDto;
import com.joinus.edu.analytics.model.entity.PersonalExam;
import com.joinus.edu.analytics.model.param.StudentInfoParam;
import com.joinus.edu.analytics.service.PersonalExamService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【personal_exam】的数据库操作Service实现
 * @createDate 2025-03-11 09:48:14
 */
@Slf4j
@Service
public class PersonalExamServiceImpl extends ServiceImpl<PersonalExamMapper, PersonalExam>
        implements PersonalExamService {
    @Override
    public IPage<PersonalExamDto> getStudentExamPage(StudentInfoParam param) {
        IPage<PersonalExamDto> page = param.page();
        page = baseMapper.getStudentExamPage(page, param);
        return page;
    }
}
