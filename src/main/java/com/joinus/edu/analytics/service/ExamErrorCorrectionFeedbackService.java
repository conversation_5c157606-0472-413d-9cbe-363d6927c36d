package com.joinus.edu.analytics.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.joinus.edu.analytics.model.param.ExamErrorCorrectionFeedbackParam;
import com.joinus.edu.analytics.model.result.ExamErrorCorrectionFeedbackPo;

/**
 * 试卷纠错service
 */
public interface ExamErrorCorrectionFeedbackService {

    /**
     * 查询试卷纠错分页
     * @param param
     * @return
     */
    IPage<ExamErrorCorrectionFeedbackPo> getExamCorrectionResultList(ExamErrorCorrectionFeedbackParam param);
}
