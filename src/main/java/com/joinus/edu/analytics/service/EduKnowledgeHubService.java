package com.joinus.edu.analytics.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.joinus.edu.analytics.model.dto.MathCatalogNodeDto;
import com.joinus.edu.analytics.model.dto.MathKnowledgePointDto;
import com.joinus.edu.analytics.model.dto.QuestionTypeStats;
import com.joinus.edu.analytics.model.dto.StudentAnalyticsResult;
import com.joinus.edu.analytics.model.enums.PublisherEnum;
import com.joinus.edu.analytics.model.param.KnowledgePointAnalyticeParam;
import com.joinus.edu.analytics.model.param.RecentExamAnalyticsParam;
import com.joinus.edu.analytics.model.param.StudentInfoParam;
import com.joinus.edu.analytics.model.result.MathKnowledgePointResult;
import com.joinus.edu.analytics.model.result.RecentExamAnalyticsResult;
import com.joinus.edu.analytics.model.result.StudentKnowledgePointAnalyticsDto;

import java.util.List;
import java.util.Set;
import java.util.UUID;

public interface EduKnowledgeHubService {

    List<MathCatalogNodeDto> listCatalogNodes(Integer grade, PublisherEnum publisher, Integer semester);

    List<MathKnowledgePointDto> listKnowledgePointsByNodeIds(Set<UUID> nodeIds);

    List<MathCatalogNodeDto> listCatalogNodesByKpIds(List<UUID> weakKpIds);
}
