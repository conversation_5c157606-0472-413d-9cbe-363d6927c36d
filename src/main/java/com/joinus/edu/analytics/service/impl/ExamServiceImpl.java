package com.joinus.edu.analytics.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.joinus.edu.analytics.mapper.ExamMapper;
import com.joinus.edu.analytics.model.dto.ExamInfoDto;
import com.joinus.edu.analytics.model.dto.PresignedUrlDto;
import com.joinus.edu.analytics.model.enums.OssEnum;
import com.joinus.edu.analytics.model.enums.QuestionDifficultyType;
import com.joinus.edu.analytics.model.enums.QuestionTypeEnum;
import com.joinus.edu.analytics.model.result.ExamInfoResult;
import com.joinus.edu.analytics.service.ExamService;
import com.joinus.edu.analytics.utils.OkHttpUtil;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class ExamServiceImpl implements ExamService {

    @Resource
    private ExamMapper examMapper;
    @Resource
    private OkHttpUtil okHttpUtil;
    @Value("${edu-knowledge-hub-host-url:https://edu-knowledge-hub.uat.qingyulan.net}")
    private String eduKnowLedgeHubHostUrl;

    @Override
    public ExamInfoResult getExamInfo(UUID examId) {
        List<ExamInfoDto> examInfo = examMapper.getExamInfo(examId);
        ExamInfoResult.ExamInfoResultBuilder builder = ExamInfoResult.builder();
        if (CollUtil.isEmpty(examInfo)) {
            return builder.build();
        }
        builder.examId(examId);
        builder.examName(examInfo.get(0).getExamName());
        List<ExamInfoResult.Data> dataList = new ArrayList<>();
        examInfo.forEach(examInfoDto -> {
            String ossUrl = examInfoDto.getOssUrl();
            ExamInfoResult.Data data = new ExamInfoResult.Data();
            BeanUtil.copyProperties(examInfoDto, data);
            if (StrUtil.isNotBlank(ossUrl)) {
                //ossUrl转List并保持顺序
                LinkedHashSet<String> ossKeySet = new LinkedHashSet<>(StrUtil.split(ossUrl, ","));
                List<String> ossKeyList = new ArrayList<>(ossKeySet);
                //翻转顺序
                Collections.reverse(ossKeyList);
                List<String> ossUrlList = new ArrayList<>();
                ossKeyList.forEach(ossUrlItem -> {
                    Map<String, Object> params = new HashMap<>(2);
                    params.put("ossEnum", OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB);
                    params.put("ossKey", ossUrlItem);
                    String response = okHttpUtil.get(eduKnowLedgeHubHostUrl + "/api/edu-knowledge-hub/ai/ability/oss/presigned-url", params);
                    PresignedUrlDto presignedUrlDto = JSONUtil.toBean(response, PresignedUrlDto.class);
                    ossUrlList.add(presignedUrlDto.getData().getPresignedUrl());
                });
                data.setOssUrl(ossUrlList);
            }
            //设置难易度
            data.setDifficultyStr(QuestionDifficultyType.getDesc(examInfoDto.getDifficulty()));
            dataList.add(data);
        });
        // 检查sortNo是否有重复值
        Set<Integer> sortNoSet = new HashSet<>();
        boolean hasDuplicateSortNo = false;
        for (ExamInfoResult.Data data : dataList) {
            if (!sortNoSet.add(data.getSortNo())) {
                hasDuplicateSortNo = true;
                break;
            }
        }

        if (hasDuplicateSortNo) {
            // 如果sortNo有重复，按照QuestionTypeEnum枚举中的顺序进行排序
            dataList.sort((o1, o2) -> {
                QuestionTypeEnum type1 = QuestionTypeEnum.getEnumByDesc(o1.getQuestionType());
                QuestionTypeEnum type2 = QuestionTypeEnum.getEnumByDesc(o2.getQuestionType());
                // 先按题型排序
                int typeCompare = type1.ordinal() - type2.ordinal();
                if (typeCompare != 0) {
                    return typeCompare;
                }
                // 如果题型相同，则按sortNo排序
                return o1.getSortNo() - o2.getSortNo();
            });
        }else{
            dataList.sort(Comparator.comparing(ExamInfoResult.Data::getSortNo));
        }

        return builder.examList(dataList).build();
    }

}
