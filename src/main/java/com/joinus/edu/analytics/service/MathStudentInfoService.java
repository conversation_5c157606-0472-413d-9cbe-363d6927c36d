package com.joinus.edu.analytics.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.edu.analytics.model.dto.StudentInfoPlanDto;
import com.joinus.edu.analytics.model.entity.MathStudentInfo;
import com.joinus.edu.analytics.model.param.StudentInfoParam;

public interface MathStudentInfoService extends IService<MathStudentInfo> {
    // 获取学生学习计划列表
    IPage<StudentInfoPlanDto> getStudentPlanPage(StudentInfoParam param);
    // 修改学生学习计划状态
    boolean changeStudentPlanStatus(Long studentId, Boolean studyPlanEnabled);
}
