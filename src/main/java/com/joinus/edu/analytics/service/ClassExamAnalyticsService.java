package com.joinus.edu.analytics.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.joinus.edu.analytics.model.dto.ClassQuestionStatisticsDto;
import com.joinus.edu.analytics.model.entity.ClassStudentExamResultEntity;
import com.joinus.edu.analytics.model.enums.QuestionAnalysisFilterEnum;
import com.joinus.edu.analytics.model.param.KnowledgePointAnalyticeParam;
import com.joinus.edu.analytics.model.param.QuestionAnalysisParam;
import com.joinus.edu.analytics.model.param.StudentAnalyticsParam;
import com.joinus.edu.analytics.model.result.ClassKnowledgePointAnalyticsResult;
import com.joinus.edu.analytics.model.result.ExamDetailsResult;
import com.joinus.edu.analytics.model.result.TopWrongQuestionResult;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 班级考试分析服务接口
 */
public interface ClassExamAnalyticsService {

    /**
     * 获取学生考情分析报告
     *
     * @param param examId 考试ID
     *              classId 班级ID
     *              pageNum 页码
     *              pageSize 每页大小
     * @return 分页数据，包含学生考情分析报告列表
     */
    IPage<ClassStudentExamResultEntity> getStudentExamReport(StudentAnalyticsParam param);

    /**
     * 高频错题TOP5
     *
     * @param examId  考试id
     * @param classId 班级id
     * @return 错题列表
     */
    List<TopWrongQuestionResult> getTopWrongQuestions(UUID examId, Long classId);

    /**
     * 获取详细题目分析数据
     *
     * @param param examId 考试ID
     *              classId 班级ID
     *              filterType 过滤类型（所有题目/错题）
     *              pageNum 页码
     *              pageSize 每页大小
     * @return 分页数据，包含题目分析列表
     */
    IPage<ClassQuestionStatisticsDto> getQuestionAnalysis(QuestionAnalysisParam param);

    /**
     * 知识点详细分析
     */
    IPage<ClassKnowledgePointAnalyticsResult> getKnowledgePointAnalysis(KnowledgePointAnalyticeParam param);

    /**
     * 获取试卷详情
     * @param examId 试卷id
     * @param classId 班级id
     * @return 试卷详情
     */
    ExamDetailsResult getExamDetails(UUID examId, Long classId);
}
