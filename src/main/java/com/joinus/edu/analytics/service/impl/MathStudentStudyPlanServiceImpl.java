package com.joinus.edu.analytics.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.edu.analytics.mapper.MathKnowledgePointMapper;
import com.joinus.edu.analytics.mapper.MathStudentStudyPlanMapper;
import com.joinus.edu.analytics.model.entity.MathStudentStudyPlan;
import com.joinus.edu.analytics.model.param.StudentPlanParam;
import com.joinus.edu.analytics.model.po.MathKnowledgePointPo;
import com.joinus.edu.analytics.model.result.MathStudentPlanResult;
import com.joinus.edu.analytics.model.vo.MathStudentStudyPlanVo;
import com.joinus.edu.analytics.service.MathKnowledgePointService;
import com.joinus.edu.analytics.service.MathStudentStudyPlanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【math_student_study_plan(学生学习计划)】的数据库操作Service实现
* @createDate 2025-09-12 16:19:30
*/
@Service
public class MathStudentStudyPlanServiceImpl extends ServiceImpl<MathStudentStudyPlanMapper, MathStudentStudyPlan>
    implements MathStudentStudyPlanService {

    @Autowired
    private MathKnowledgePointService mathKnowledgePointService;

    @Override
    public List<MathStudentPlanResult> listByStudentId(Long studentId) {
        List<MathStudentStudyPlan> plans = lambdaQuery().eq(MathStudentStudyPlan::getStudentId, studentId)
                .list();
        if (CollUtil.isEmpty(plans)) {
            return List.of();
        }

        List<MathKnowledgePointPo> mathKnowledgePointPos = mathKnowledgePointService.listByIds(plans.stream().map(MathStudentStudyPlan::getKnowledgePointId).toList());
        Map<UUID, MathKnowledgePointPo> kpMap = mathKnowledgePointPos.stream().collect(Collectors.toMap(MathKnowledgePointPo::getId, kp-> kp));

        //TODO 补充掌握度

        List<MathStudentPlanResult> results = plans.stream()
                .map(plan -> MathStudentPlanResult.builder()
                        .knowledgePointId(plan.getKnowledgePointId())
                        .knowledgePointName(kpMap.get(plan.getKnowledgePointId()).getName())
                        .weekNo(plan.getWeekNo())
                        .sortNo(plan.getSortNo())
                        .build())
                .toList();
        return results.stream().sorted(Comparator.comparing(MathStudentPlanResult::getWeekNo)
                .thenComparing(MathStudentPlanResult::getSortNo))
                .toList();
    }

    @Override
    public void removeByStudentId(Long studentId) {
        baseMapper.delete(Wrappers.lambdaQuery(MathStudentStudyPlan.class).eq(MathStudentStudyPlan::getStudentId, studentId));
    }
}




