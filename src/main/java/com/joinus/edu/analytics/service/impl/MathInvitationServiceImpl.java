package com.joinus.edu.analytics.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.joinus.edu.analytics.mapper.MathInvitationMapper;
import com.joinus.edu.analytics.model.param.MathInvitationParam;
import com.joinus.edu.analytics.model.result.MathInvitationPo;
import com.joinus.edu.analytics.service.MathInvitationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* @description 针对表【math_invitation(数学邀请记录表)】的数据库操作Service实现
*/
@Service
public class MathInvitationServiceImpl implements MathInvitationService {

    @Autowired
    private MathInvitationMapper mathInvitationMapper;

    @Override
    public IPage<MathInvitationPo> getMathInvitationResultList(MathInvitationParam param) {
        IPage<MathInvitationPo> mathInvitationList = mathInvitationMapper.selectMathInvitationList(param.page(), param);
        return mathInvitationList;
    }
}




